from setuptools import setup, find_packages

# Read requirements from file
with open('requirements.txt') as f:
    requirements = []
    for line in f:
        line = line.strip()
        # Skip comments and empty lines
        if line and not line.startswith('#'):
            requirements.append(line)

setup(
    name="gosae",
    version="0.1.0",
    packages=find_packages(),
    install_requires=requirements,
    author="GenomeOcean Team",
    author_email="<EMAIL>",
    description="Sparse Autoencoder for GenomeOcean",
    long_description=open('README.md').read(),
    long_description_content_type="text/markdown",
    keywords="sparse autoencoder, genomics, language models, mistral, genomeocean",
    python_requires=">=3.11",
    classifiers=[
        "Development Status :: 3 - Alpha",
        "Intended Audience :: Science/Research",
        "License :: OSI Approved :: MIT License",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.11",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
        "Topic :: Scientific/Engineering :: Bio-Informatics",
    ],
    url="https://github.com/jgi-genomeocean/gosae",
    project_urls={
        "Bug Tracker": "https://github.com/jgi-genomeocean/gosae/issues",
        "Documentation": "https://github.com/jgi-genomeocean/gosae/blob/main/README.md",
        "Source Code": "https://github.com/jgi-genomeocean/gosae",
    },
)

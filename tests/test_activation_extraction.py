#!/usr/bin/env python
"""
Test script for activation extraction from GenomeOcean model.

This script tests the activation extraction functionality of the GOSAE framework,
including extracting activations from specific layers of the GenomeOcean model.
"""

import os
import argparse
import torch
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.utils.config import GenomeOceanConfig
from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_token_limit


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Test activation extraction.")

    parser.add_argument("--data_file", type=str, help="Path to genomic data file (FASTA or GenBank).")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name or path.")
    # Get default target layers from GenomeOceanConfig
    default_config = GenomeOceanConfig()
    parser.add_argument("--target_layers", type=str, nargs="+",
                        default=default_config.target_layers,
                        help="Target layers for activation extraction.")
    parser.add_argument("--output_dir", type=str, default="test_outputs", help="Output directory.")
    parser.add_argument("--create_sample", action="store_true", help="Create a sample FASTA file if no data file is provided.")
    parser.add_argument("--use_cache", action="store_true", help="Whether to use cached activations.")
    parser.add_argument("--batch_size", type=int, default=2, help="Batch size for processing.")
    parser.add_argument("--max_sequences", type=int, default=5, help="Maximum number of sequences to process.")

    return parser.parse_args()


def create_sample_fasta(output_path):
    """Create a sample FASTA file for testing."""
    print(f"Creating sample FASTA file at {output_path}")

    # Create some sample sequences
    sequences = [
        (">sample1", "ATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGC"),
        (">sample2", "GGGCCCAAATTTGGGCCCAAATTTGGGCCCAAATTTGGGCCCAAATTTGGGCCCAAATTT"),
        (">sample3", "NNNNNATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCNNNNN"),
        (">sample4", "ATATATATATATATATATATATATATATATATATATATATATATATATATATATATATAT"),
        (">sample5", "GCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGC"),
    ]

    # Write to file
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, "w") as f:
        for header, seq in sequences:
            f.write(f"{header}\n{seq}\n")

    return output_path


def test_activation_extraction(extractor, sequences, target_layer, batch_size, output_dir):
    """Test extracting activations from a specific layer."""
    print(f"\n=== Testing activation extraction from layer: {target_layer} ===")

    # Extract activations
    activations = extractor.extract_activations(
        sequences=sequences,
        layer_name=target_layer,
        batch_size=batch_size,
    )

    # Print information
    print(f"Extracted activations with shape: {activations.shape}")
    print(f"Activation statistics:")
    print(f"  Mean: {torch.mean(activations).item():.4f}")
    print(f"  Std: {torch.std(activations).item():.4f}")
    print(f"  Min: {torch.min(activations).item():.4f}")
    print(f"  Max: {torch.max(activations).item():.4f}")

    # Visualize activations
    visualize_activations(activations, output_dir, target_layer)

    return activations


def test_activation_stats(extractor, sequences, target_layer, batch_size):
    """Test computing activation statistics."""
    print(f"\n=== Testing activation statistics for layer: {target_layer} ===")

    # Compute activation statistics
    stats = extractor.get_activation_stats(
        sequences=sequences,
        layer_name=target_layer,
        batch_size=batch_size,
    )

    # Print statistics
    print(f"Activation statistics:")
    for key, value in stats.items():
        print(f"  {key}: {value}")

    return stats


def visualize_activations(activations, output_dir, layer_name):
    """Visualize activations."""
    print(f"\n=== Visualizing activations ===")

    # Create output directory
    vis_dir = os.path.join(output_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)

    # Flatten activations for histogram and convert to float32 (to handle BFloat16)
    flat_activations = activations.to(torch.float32).flatten().cpu().numpy()
    print(f"Converted activations from {activations.dtype} to float32 for visualization")

    # Create histogram
    plt.figure(figsize=(10, 6))
    plt.hist(flat_activations, bins=50, alpha=0.7)
    plt.title(f"Activation Distribution for {layer_name}")
    plt.xlabel("Activation Value")
    plt.ylabel("Frequency")
    plt.grid(True, alpha=0.3)

    # Save figure
    hist_path = os.path.join(vis_dir, f"{layer_name.replace('.', '_')}_histogram.png")
    plt.savefig(hist_path)
    plt.close()
    print(f"Saved activation histogram to {hist_path}")

    # Create heatmap of first few activations (convert to float32 for BFloat16 compatibility)
    plt.figure(figsize=(12, 8))
    plt.imshow(activations[0, :10, :10].to(torch.float32).cpu().numpy(), cmap="viridis")
    plt.colorbar()
    plt.title(f"Activation Heatmap for {layer_name} (First 10x10)")
    plt.xlabel("Feature Dimension")
    plt.ylabel("Token Position")

    # Save figure
    heatmap_path = os.path.join(vis_dir, f"{layer_name.replace('.', '_')}_heatmap.png")
    plt.savefig(heatmap_path)
    plt.close()
    print(f"Saved activation heatmap to {heatmap_path}")


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Create or use data file
    if args.data_file:
        data_file = args.data_file
    elif args.create_sample:
        data_file = create_sample_fasta(os.path.join(args.output_dir, "sample.fa"))
    else:
        raise ValueError("Either provide a data file with --data_file or use --create_sample to create a sample file")

    # Initialize data processor
    print(f"\n=== Initializing GenomicDataProcessor for model: {args.model} ===")
    data_processor = GenomicDataProcessor(
        tokenizer_model=args.model,
    )

    # Load and process sequences
    seq_records = data_processor.load_fasta(data_file)
    processed_sequences = data_processor.process_sequences(seq_records)

    # Limit number of sequences if needed
    if args.max_sequences > 0 and len(processed_sequences) > args.max_sequences:
        processed_sequences = processed_sequences[:args.max_sequences]
        print(f"Limited to {args.max_sequences} sequences")

    # Initialize activation extractor
    print(f"\n=== Initializing GenomeOceanActivationExtractor for model: {args.model} ===")
    config = GenomeOceanConfig(
        model_name=args.model,
        target_layers=args.target_layers,
        cache_dir=os.path.join(args.output_dir, "cache"),
        use_cache=args.use_cache,
    )
    extractor = GenomeOceanActivationExtractor(config)

    # Process each target layer
    all_activations = {}
    for target_layer in args.target_layers:
        print(f"\n=== Processing target layer: {target_layer} ===")

        # Create layer-specific output directory
        layer_output_dir = os.path.join(args.output_dir, target_layer.replace(".", "_"))
        os.makedirs(layer_output_dir, exist_ok=True)

        # Test activation extraction
        activations = test_activation_extraction(
            extractor=extractor,
            sequences=processed_sequences,
            target_layer=target_layer,
            batch_size=args.batch_size,
            output_dir=layer_output_dir,
        )
        all_activations[target_layer] = activations

        # Test activation statistics
        stats = test_activation_stats(
            extractor=extractor,
            sequences=processed_sequences,
            target_layer=target_layer,
            batch_size=args.batch_size,
        )

        # Save activations to layer-specific file
        activation_path = os.path.join(layer_output_dir, f"{target_layer.replace('.', '_')}_activations.pt")
        torch.save(activations, activation_path)
        print(f"\nSaved activations to {activation_path}")

        # Also save the activations to the main output directory for use by other tests
        # We'll use the last target layer in the list
        if target_layer == args.target_layers[-1]:
            main_activation_path = os.path.join(args.output_dir, "activations.pt")
            torch.save(activations, main_activation_path)
            print(f"\nSaved activations for other tests to {main_activation_path}")

    print("\n=== All tests completed successfully! ===")


if __name__ == "__main__":
    main()

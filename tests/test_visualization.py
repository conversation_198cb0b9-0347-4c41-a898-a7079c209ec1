#!/usr/bin/env python
"""
Test script for visualizing Sparse Autoencoder features.

This script tests the visualization functionality of the GOSAE framework,
including visualizing features discovered by a trained Sparse Autoencoder.
"""

import os
import argparse
import torch
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.visualization.feature_visualizer import FeatureVisualizer
from gosae.utils.genomeocean_utils import DEFAULT_MODEL


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Test feature visualization.")

    parser.add_argument("--model_file", type=str, help="Path to trained Sparse Autoencoder model (.pt).")
    parser.add_argument("--activations_file", type=str, help="Path to saved activations file (.pt).")
    parser.add_argument("--output_dir", type=str, default="test_outputs/visualization", help="Output directory.")
    parser.add_argument("--create_dummy", action="store_true", help="Create dummy model and activations if not provided.")
    parser.add_argument("--top_n", type=int, default=20, help="Number of top features to visualize.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use.")

    return parser.parse_args()


def create_dummy_model_and_activations(model_path, activations_path, input_dim=768, latent_dim=1536, num_samples=100):
    """Create a dummy model and activations for testing."""
    print(f"Creating dummy model at {model_path}")

    # Create SAE configuration
    from gosae.utils.config import SparseAutoencoderConfig
    config = SparseAutoencoderConfig(
        input_dim=input_dim,
        latent_dim=latent_dim,
        hidden_dim=input_dim,
    )

    # Create SAE
    model = SparseAutoencoder(config)

    # Save the model
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    model.save(model_path)

    print(f"Created dummy model with input_dim={input_dim}, latent_dim={latent_dim}")

    # Create dummy activations
    print(f"Creating dummy activations at {activations_path}")

    # Create input activations
    input_activations = torch.randn(num_samples, input_dim)

    # Get feature activations
    with torch.no_grad():
        _, feature_activations = model(input_activations)

    # Save activations
    os.makedirs(os.path.dirname(activations_path), exist_ok=True)
    torch.save(feature_activations, activations_path)

    print(f"Created dummy feature activations with shape: {feature_activations.shape}")

    return model, feature_activations


def test_feature_activations(visualizer, activations, feature_idx, output_dir):
    """Test visualizing feature activations."""
    print(f"\n=== Testing feature activations visualization for feature {feature_idx} ===")

    # Check if activations are 3D (batch_size, sequence_length, hidden_dim)
    if len(activations.shape) == 3:
        print(f"Reshaping activations from {activations.shape} to 2D for visualization")
        # Reshape to 2D: (batch_size * sequence_length, hidden_dim)
        batch_size, seq_len, hidden_dim = activations.shape
        activations_2d = activations.reshape(-1, hidden_dim)

        # Use the reshaped activations
        activations_to_use = activations_2d

        # Create sequence IDs for the reshaped activations
        # Just use the first few sequences to avoid too many bars
        max_sequences = min(10, batch_size * seq_len)
        sequence_ids = [f"Seq {i+1}" for i in range(max_sequences)]
    else:
        # Activations are already 2D
        activations_to_use = activations
        max_sequences = min(10, activations.shape[0])
        sequence_ids = [f"Sequence {i+1}" for i in range(max_sequences)]

    print(f"Using activations with shape: {activations_to_use.shape}")
    print(f"Using {len(sequence_ids)} sequence IDs")

    # Visualize feature activations
    fig = visualizer.plot_feature_activations(
        activations=activations_to_use[:max_sequences],
        feature_idx=feature_idx,
        sequence_ids=sequence_ids,
        title=f"Feature {feature_idx} Activations",
        save_path=os.path.join(output_dir, f"feature_{feature_idx}_activations.png"),
    )

    print(f"Visualized activations for feature {feature_idx}")

    return fig


def test_feature_heatmap(visualizer, activations, feature_indices, output_dir):
    """Test visualizing feature heatmap."""
    print(f"\n=== Testing feature heatmap visualization ===")

    # Check if activations are 3D (batch_size, sequence_length, hidden_dim)
    if len(activations.shape) == 3:
        print(f"Reshaping activations from {activations.shape} to 2D for visualization")
        # Reshape to 2D: (batch_size * sequence_length, hidden_dim)
        batch_size, seq_len, hidden_dim = activations.shape
        activations_2d = activations.reshape(-1, hidden_dim)

        # Use the reshaped activations
        activations_to_use = activations_2d

        # Create sequence IDs for the reshaped activations
        # Just use the first few sequences to avoid too many rows
        max_sequences = min(10, batch_size * seq_len)
        sequence_ids = [f"Seq {i+1}" for i in range(max_sequences)]
    else:
        # Activations are already 2D
        activations_to_use = activations
        max_sequences = min(10, activations.shape[0])
        sequence_ids = [f"Sequence {i+1}" for i in range(max_sequences)]

    print(f"Using activations with shape: {activations_to_use.shape}")
    print(f"Using {len(sequence_ids)} sequence IDs")

    # Visualize feature heatmap
    fig = visualizer.plot_feature_heatmap(
        activations=activations_to_use[:max_sequences],
        feature_indices=feature_indices,
        sequence_ids=sequence_ids,
        title=f"Top {len(feature_indices)} Feature Activations Heatmap",
        save_path=os.path.join(output_dir, "feature_heatmap.png"),
    )

    print(f"Visualized feature heatmap for {len(feature_indices)} features")

    return fig


def test_feature_embedding(visualizer, decoder_weights, feature_indices, output_dir):
    """Test visualizing feature embedding."""
    print(f"\n=== Testing feature embedding visualization ===")

    # Visualize feature embedding with PCA
    pca_fig = visualizer.plot_feature_embedding(
        decoder_weights=decoder_weights,
        method="pca",
        feature_indices=feature_indices,
        title=f"Top {len(feature_indices)} Feature Embedding (PCA)",
        save_path=os.path.join(output_dir, "feature_embedding_pca.png"),
    )

    print(f"Visualized feature embedding with PCA")

    # Visualize feature embedding with t-SNE
    # Ensure we have enough samples for t-SNE (at least 5)
    if len(feature_indices) >= 5:
        tsne_fig = visualizer.plot_feature_embedding(
            decoder_weights=decoder_weights,
            method="tsne",
            feature_indices=feature_indices,
            title=f"Top {len(feature_indices)} Feature Embedding (t-SNE)",
            save_path=os.path.join(output_dir, "feature_embedding_tsne.png"),
        )

        print(f"Visualized feature embedding with t-SNE")
    else:
        print(f"Skipping t-SNE visualization: not enough samples (need at least 5, got {len(feature_indices)})")
        tsne_fig = None

    return pca_fig, tsne_fig


def test_feature_importance(visualizer, decoder_weights, top_n, output_dir):
    """Test visualizing feature importance."""
    print(f"\n=== Testing feature importance visualization ===")

    # Compute feature importance (L1 norm of decoder weights)
    # Detach the tensor before converting to numpy to avoid the 'requires_grad' error
    # Convert to float32 first to avoid issues with unsupported data types like BFloat16
    feature_importance = torch.norm(decoder_weights, dim=0).detach().to(dtype=torch.float32).cpu().numpy()
    feature_importance_dict = {i: float(importance) for i, importance in enumerate(feature_importance)}

    # Visualize feature importance
    fig = visualizer.plot_feature_importance(
        feature_importances=feature_importance_dict,
        top_n=top_n,
        title=f"Top {top_n} Feature Importances",
        save_path=os.path.join(output_dir, "feature_importance.png"),
    )

    print(f"Visualized importance for top {top_n} features")

    # Get indices of top features
    top_indices = sorted(feature_importance_dict.keys(), key=lambda k: feature_importance_dict[k], reverse=True)[:top_n]

    return fig, top_indices


def test_feature_correlation(visualizer, activations, feature_indices, output_dir):
    """Test visualizing feature correlation."""
    print(f"\n=== Testing feature correlation visualization ===")

    # Check if activations are 3D (batch_size, sequence_length, hidden_dim)
    if len(activations.shape) == 3:
        print(f"Reshaping activations from {activations.shape} to 2D for correlation visualization")
        # Reshape to 2D: (batch_size * sequence_length, hidden_dim)
        batch_size, seq_len, hidden_dim = activations.shape
        activations_2d = activations.reshape(-1, hidden_dim)

        # Use the reshaped activations
        activations_to_use = activations_2d
    else:
        # Activations are already 2D
        activations_to_use = activations

    print(f"Using activations with shape: {activations_to_use.shape}")

    # Ensure feature indices are valid for the activations
    if activations_to_use.shape[1] < max(feature_indices):
        print(f"Warning: Some feature indices are out of bounds. Filtering invalid indices.")
        valid_indices = [idx for idx in feature_indices if idx < activations_to_use.shape[1]]
        if len(valid_indices) < 2:
            print(f"Not enough valid feature indices for correlation plot. Need at least 2.")
            return None
        feature_indices = valid_indices[:min(10, len(valid_indices))]  # Limit to 10 features

    # Visualize feature correlation
    fig = visualizer.plot_feature_correlation(
        activations=activations_to_use,
        feature_indices=feature_indices,
        title=f"Top {len(feature_indices)} Feature Correlations",
        save_path=os.path.join(output_dir, "feature_correlation.png"),
    )

    print(f"Visualized correlation for {len(feature_indices)} features")

    return fig


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load or create model and activations
    if args.model_file and args.activations_file:
        print(f"Loading model from {args.model_file}")
        model = SparseAutoencoder.load(args.model_file, device=args.device)

        print(f"Loading activations from {args.activations_file}")
        activations = torch.load(args.activations_file, map_location=args.device)
    elif args.create_dummy:
        model_path = os.path.join(args.output_dir, "dummy_model.pt")
        activations_path = os.path.join(args.output_dir, "dummy_activations.pt")
        model, activations = create_dummy_model_and_activations(model_path, activations_path)
    else:
        raise ValueError("Either provide model and activations files or use --create_dummy to create dummy data")

    # Print model and activations information
    print(f"\nModel information:")
    print(f"  Input dimension: {model.input_dim}")
    print(f"  Hidden dimension: {model.hidden_dim}")
    print(f"  Latent dimension: {model.latent_dim}")

    print(f"\nActivations shape: {activations.shape}")

    # Create visualizer
    visualizer = FeatureVisualizer(output_dir=args.output_dir)

    # Test feature importance visualization
    _, top_indices = test_feature_importance(
        visualizer=visualizer,
        decoder_weights=model.decoder.weight,
        top_n=args.top_n,
        output_dir=args.output_dir,
    )

    # Test feature activations visualization for top features
    # Note: top_indices are for the latent dimension, but activations are for the input dimension
    # So we need to use indices that are valid for the activations

    # Get the valid feature index range for the activations
    if len(activations.shape) == 3:
        _, _, num_features = activations.shape
    else:
        _, num_features = activations.shape

    print(f"Activations have {num_features} features")
    print(f"Top indices from feature importance: {top_indices[:5]}")

    # Use valid feature indices for the activations
    valid_feature_indices = [min(idx % num_features, num_features - 1) for idx in top_indices[:5]]
    print(f"Using valid feature indices: {valid_feature_indices}")

    for feature_idx in valid_feature_indices:
        test_feature_activations(
            visualizer=visualizer,
            activations=activations,
            feature_idx=feature_idx,
            output_dir=args.output_dir,
        )

    # Test feature heatmap visualization
    # Use valid feature indices for the heatmap as well
    valid_heatmap_indices = [min(idx % num_features, num_features - 1) for idx in top_indices[:10]]
    print(f"Using valid heatmap feature indices: {valid_heatmap_indices}")

    test_feature_heatmap(
        visualizer=visualizer,
        activations=activations,
        feature_indices=valid_heatmap_indices,
        output_dir=args.output_dir,
    )

    # Test feature embedding visualization
    test_feature_embedding(
        visualizer=visualizer,
        decoder_weights=model.decoder.weight,
        feature_indices=top_indices,
        output_dir=args.output_dir,
    )

    # Test feature correlation visualization
    test_feature_correlation(
        visualizer=visualizer,
        activations=activations,
        feature_indices=top_indices[:min(10, len(top_indices))],
        output_dir=args.output_dir,
    )

    print("\n=== All tests completed successfully! ===")


if __name__ == "__main__":
    main()

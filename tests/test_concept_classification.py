#!/usr/bin/env python
"""
Test script for genome concept classification using Sparse Autoencoder features.

This script tests the concept classification functionality using features
discovered by a trained Sparse Autoencoder.
"""

import os
import argparse
import torch
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt
from sklearn.model_selection import train_test_split
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
import seaborn as sns

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.utils.config import GenomeOceanConfig
from gosae.utils.genomeocean_utils import DEFAULT_MODEL


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Test genome concept classification.")

    parser.add_argument("--model_file", type=str, help="Path to trained Sparse Autoencoder model (.pt).")
    parser.add_argument("--data_dir", type=str, help="Directory containing genomic data files.")
    parser.add_argument("--output_dir", type=str, default="test_outputs/concept_classification", help="Output directory.")
    parser.add_argument("--create_dummy", action="store_true", help="Create dummy data if not provided.")
    parser.add_argument("--genomeocean_model", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name or path.")
    parser.add_argument("--target_layer", type=str, default="model.layers.8.mlp.up_proj", help="Target layer for activation extraction.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use.")

    return parser.parse_args()


def create_dummy_data(output_dir, num_samples=100, num_classes=3, input_dim=768, latent_dim=1536):
    """Create dummy data for testing."""
    print(f"Creating dummy data in {output_dir}")

    # Create SAE configuration
    from gosae.utils.config import SparseAutoencoderConfig
    config = SparseAutoencoderConfig(
        input_dim=input_dim,
        latent_dim=latent_dim,
        hidden_dim=input_dim,
    )

    # Create SAE
    model = SparseAutoencoder(config)

    # Save the model
    model_path = os.path.join(output_dir, "dummy_model.pt")
    os.makedirs(os.path.dirname(model_path), exist_ok=True)
    model.save(model_path)

    print(f"Created dummy model with input_dim={input_dim}, latent_dim={latent_dim}")

    # Create dummy activations
    activations = torch.randn(num_samples, input_dim)

    # Get feature activations
    with torch.no_grad():
        _, feature_activations = model(activations)

    # Create dummy labels
    labels = np.random.randint(0, num_classes, size=num_samples)

    # Create dummy class names
    class_names = [f"Class_{i}" for i in range(num_classes)]

    # Create dummy data directory
    data_dir = os.path.join(output_dir, "dummy_data")
    os.makedirs(data_dir, exist_ok=True)

    # Save activations and labels
    activations_path = os.path.join(data_dir, "activations.pt")
    torch.save(feature_activations, activations_path)

    labels_path = os.path.join(data_dir, "labels.npy")
    np.save(labels_path, labels)

    class_names_path = os.path.join(data_dir, "class_names.npy")
    np.save(class_names_path, np.array(class_names))

    print(f"Created dummy data with {num_samples} samples and {num_classes} classes")

    return model, feature_activations, labels, class_names, data_dir


def extract_features(model, activations):
    """Extract features using the Sparse Autoencoder."""
    print(f"\n=== Extracting features using Sparse Autoencoder ===")

    # Ensure activations are on the correct device
    activations = activations.to(model.config.device)

    # Check if activations are already feature activations
    if activations.shape[1] == model.latent_dim:
        print(f"Activations already have latent dimension shape: {activations.shape}")
        features = activations
    else:
        # Extract features by passing through the model
        print(f"Passing activations through model to extract features")
        with torch.no_grad():
            _, features = model(activations)

    # Convert to numpy
    features = features.cpu().numpy()

    print(f"Extracted features with shape: {features.shape}")

    return features


def train_classifier(features, labels, class_names, test_size=0.2, random_state=42):
    """Train a classifier on the extracted features."""
    print(f"\n=== Training classifier on extracted features ===")

    # Split into train and test sets
    X_train, X_test, y_train, y_test = train_test_split(
        features, labels, test_size=test_size, random_state=random_state, stratify=labels
    )

    print(f"Training set: {X_train.shape[0]} samples")
    print(f"Test set: {X_test.shape[0]} samples")

    # Train a logistic regression classifier
    classifier = LogisticRegression(max_iter=1000, random_state=random_state)
    classifier.fit(X_train, y_train)

    # Evaluate on test set
    y_pred = classifier.predict(X_test)
    accuracy = accuracy_score(y_test, y_pred)

    print(f"Test accuracy: {accuracy:.4f}")

    # Print classification report
    print("\nClassification Report:")
    print(classification_report(y_test, y_pred, target_names=class_names))

    return classifier, X_train, X_test, y_train, y_test, y_pred


def visualize_results(classifier, X_test, y_test, y_pred, class_names, output_dir):
    """Visualize classification results."""
    print(f"\n=== Visualizing classification results ===")

    # Create output directory
    vis_dir = os.path.join(output_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)

    # Plot confusion matrix
    plt.figure(figsize=(10, 8))
    cm = confusion_matrix(y_test, y_pred)
    sns.heatmap(cm, annot=True, fmt="d", cmap="Blues", xticklabels=class_names, yticklabels=class_names)
    plt.title("Confusion Matrix")
    plt.xlabel("Predicted Label")
    plt.ylabel("True Label")

    # Save figure
    cm_path = os.path.join(vis_dir, "confusion_matrix.png")
    plt.savefig(cm_path)
    plt.close()
    print(f"Saved confusion matrix to {cm_path}")

    # Plot feature importance
    if hasattr(classifier, "coef_"):
        plt.figure(figsize=(12, 8))

        # Get feature importance
        importance = np.abs(classifier.coef_)

        # For multiclass, average across classes
        if importance.shape[0] > 1:
            importance = np.mean(importance, axis=0)
        else:
            importance = importance.flatten()

        # Get top features
        top_n = min(20, importance.shape[0])
        top_indices = np.argsort(importance)[-top_n:]
        top_importance = importance[top_indices]

        # Plot
        plt.barh(range(top_n), top_importance, align="center")
        plt.yticks(range(top_n), [f"Feature {i}" for i in top_indices])
        plt.title(f"Top {top_n} Feature Importance")
        plt.xlabel("Importance")

        # Save figure
        importance_path = os.path.join(vis_dir, "feature_importance.png")
        plt.savefig(importance_path)
        plt.close()
        print(f"Saved feature importance to {importance_path}")

    # If we have only 2 features, plot decision boundary
    if X_test.shape[1] == 2:
        plt.figure(figsize=(10, 8))

        # Create a mesh grid
        h = 0.02  # step size in the mesh
        x_min, x_max = X_test[:, 0].min() - 1, X_test[:, 0].max() + 1
        y_min, y_max = X_test[:, 1].min() - 1, X_test[:, 1].max() + 1
        xx, yy = np.meshgrid(np.arange(x_min, x_max, h), np.arange(y_min, y_max, h))

        # Predict on the mesh grid
        Z = classifier.predict(np.c_[xx.ravel(), yy.ravel()])
        Z = Z.reshape(xx.shape)

        # Plot decision boundary
        plt.contourf(xx, yy, Z, alpha=0.8, cmap=plt.cm.Paired)

        # Plot test points
        scatter = plt.scatter(X_test[:, 0], X_test[:, 1], c=y_test, edgecolors="k", cmap=plt.cm.Paired)
        plt.xlabel("Feature 1")
        plt.ylabel("Feature 2")
        plt.title("Decision Boundary")
        plt.legend(handles=scatter.legend_elements()[0], labels=class_names)

        # Save figure
        boundary_path = os.path.join(vis_dir, "decision_boundary.png")
        plt.savefig(boundary_path)
        plt.close()
        print(f"Saved decision boundary to {boundary_path}")


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load or create data
    if args.create_dummy:
        print("Creating dummy data for testing")
        model, activations, labels, class_names, data_dir = create_dummy_data(args.output_dir)
    elif args.model_file and args.data_dir:
        print(f"Loading model from {args.model_file}")
        model = SparseAutoencoder.load(args.model_file, device=args.device)

        # Load activations and labels
        activations_path = os.path.join(args.data_dir, "activations.pt")
        labels_path = os.path.join(args.data_dir, "labels.npy")
        class_names_path = os.path.join(args.data_dir, "class_names.npy")

        if os.path.exists(activations_path) and os.path.exists(labels_path) and os.path.exists(class_names_path):
            print(f"Loading data from {args.data_dir}")
            activations = torch.load(activations_path, map_location=args.device)
            labels = np.load(labels_path)
            class_names = np.load(class_names_path)
        else:
            raise ValueError(f"Data files not found in {args.data_dir}")
    else:
        raise ValueError("Either provide model and data files or use --create_dummy to create dummy data")

    # Print model and data information
    print(f"\nModel information:")
    print(f"  Input dimension: {model.input_dim}")
    print(f"  Hidden dimension: {model.hidden_dim}")
    print(f"  Latent dimension: {model.latent_dim}")

    print(f"\nData information:")
    print(f"  Number of samples: {len(labels)}")
    print(f"  Number of classes: {len(np.unique(labels))}")
    print(f"  Class names: {class_names}")
    print(f"  Activations shape: {activations.shape}")

    # Extract features
    features = extract_features(model, activations)

    # Train classifier
    classifier, X_train, X_test, y_train, y_test, y_pred = train_classifier(
        features, labels, class_names
    )

    # Visualize results
    visualize_results(
        classifier=classifier,
        X_test=X_test,
        y_test=y_test,
        y_pred=y_pred,
        class_names=class_names,
        output_dir=args.output_dir,
    )

    # Save classifier
    import joblib
    classifier_path = os.path.join(args.output_dir, "classifier.joblib")
    joblib.dump(classifier, classifier_path)
    print(f"\nSaved classifier to {classifier_path}")

    print("\n=== All tests completed successfully! ===")


if __name__ == "__main__":
    main()

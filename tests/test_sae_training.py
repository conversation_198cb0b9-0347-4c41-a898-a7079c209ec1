#!/usr/bin/env python
"""
Test script for training a Sparse Autoencoder.

This script tests the training functionality of the GOSAE framework,
including creating and training a Sparse Autoencoder on activations.
"""

import os
import argparse
import torch
import numpy as np
from pathlib import Path
import matplotlib.pyplot as plt

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.training.trainer import SparseAutoencoderTrainer
from gosae.utils.config import SparseAutoencoderConfig, TrainingConfig
from gosae.utils.genomeocean_utils import get_model_embedding_size, DEFAULT_MODEL


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Test Sparse Autoencoder training.")

    parser.add_argument("--activations_file", type=str, help="Path to saved activations file (.pt).")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name or path.")
    parser.add_argument("--output_dir", type=str, default="test_outputs/sae_training", help="Output directory.")
    parser.add_argument("--create_dummy", action="store_true", help="Create dummy activations if no file is provided.")
    parser.add_argument("--latent_dim", type=int, default=None, help="Latent dimension for the SAE (default: 2x embedding size).")
    parser.add_argument("--l1_coefficient", type=float, default=1e-3, help="L1 regularization coefficient.")
    parser.add_argument("--topk", type=int, default=32, help="BatchTopK parameter (0 to disable).")
    parser.add_argument("--batch_size", type=int, default=32, help="Batch size for training.")
    parser.add_argument("--num_epochs", type=int, default=10, help="Number of epochs for training.")
    parser.add_argument("--learning_rate", type=float, default=3e-4, help="Learning rate for training.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use.")

    return parser.parse_args()


def create_dummy_activations(output_path, model_name, num_samples=100, seq_length=10, device="cpu"):
    """Create dummy activations for testing."""
    print(f"Creating dummy activations at {output_path}")

    # Get embedding size for the model
    embedding_size = get_model_embedding_size(model_name)

    # Create dummy activations on the specified device
    activations = torch.randn(num_samples, seq_length, embedding_size, device=device)

    # Save activations
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    torch.save(activations, output_path)

    print(f"Created dummy activations with shape: {activations.shape} on device: {activations.device}")

    return activations


def test_sae_creation(input_dim, latent_dim, hidden_dim, l1_coefficient, topk, device):
    """Test creating a Sparse Autoencoder."""
    print(f"\n=== Testing Sparse Autoencoder creation ===")

    # Create SAE configuration
    config = SparseAutoencoderConfig(
        input_dim=input_dim,
        latent_dim=latent_dim,
        hidden_dim=hidden_dim,
        l1_coefficient=l1_coefficient,
        topk=topk,
        device=device,
    )

    # Create SAE
    model = SparseAutoencoder(config)

    # Print model information
    print(f"Created Sparse Autoencoder with:")
    print(f"  Input dimension: {model.input_dim}")
    print(f"  Hidden dimension: {model.hidden_dim}")
    print(f"  Latent dimension: {model.latent_dim}")
    print(f"  L1 coefficient: {model.config.l1_coefficient}")
    print(f"  TopK: {model.config.topk}")

    # Count parameters
    encoder_params = sum(p.numel() for p in model.encoder.parameters())
    decoder_params = sum(p.numel() for p in model.decoder.parameters())
    total_params = sum(p.numel() for p in model.parameters())

    print(f"  Encoder parameters: {encoder_params:,}")
    print(f"  Decoder parameters: {decoder_params:,}")
    print(f"  Total parameters: {total_params:,}")

    return model


def test_sae_forward(model, activations, batch_size=32):
    """Test forward pass through the Sparse Autoencoder."""
    print(f"\n=== Testing Sparse Autoencoder forward pass ===")

    # Reshape activations if needed (from [batch, seq_len, hidden] to [batch*seq_len, hidden])
    if len(activations.shape) == 3:
        orig_shape = activations.shape
        activations = activations.reshape(-1, activations.shape[-1])
        print(f"Reshaped activations from {orig_shape} to {activations.shape}")

    # Move to device and convert to the same dtype as the model
    model_dtype = next(model.parameters()).dtype
    print(f"Model dtype: {model_dtype}")
    print(f"Activations dtype: {activations.dtype}")

    # Convert activations to the same dtype as the model
    activations = activations.to(device=model.config.device, dtype=model_dtype)
    print(f"Converted activations to dtype: {activations.dtype} on device: {activations.device}")

    # Create a small batch
    batch_size = min(batch_size, activations.shape[0])
    batch = activations[:batch_size]

    # Forward pass
    with torch.no_grad():
        reconstructed, latent = model(batch)

    # Print information
    print(f"Forward pass with batch shape: {batch.shape}")
    print(f"  Reconstructed shape: {reconstructed.shape}")
    print(f"  Latent shape: {latent.shape}")

    # Compute reconstruction error
    mse = torch.nn.functional.mse_loss(reconstructed, batch).item()
    print(f"  Reconstruction MSE: {mse:.6f}")

    # Compute sparsity
    sparsity = torch.mean((latent > 0).float()).item()
    print(f"  Latent sparsity: {sparsity:.6f}")

    return reconstructed, latent


def test_sae_training(model, activations, output_dir, batch_size, num_epochs, learning_rate):
    """Test training the Sparse Autoencoder."""
    print(f"\n=== Testing Sparse Autoencoder training ===")

    # Reshape activations if needed (from [batch, seq_len, hidden] to [batch*seq_len, hidden])
    if len(activations.shape) == 3:
        orig_shape = activations.shape
        activations = activations.reshape(-1, activations.shape[-1])
        print(f"Reshaped activations from {orig_shape} to {activations.shape}")

    # Move to device
    activations = activations.to(model.config.device)

    # Split into train and validation sets (90/10)
    split_idx = int(0.9 * activations.shape[0])
    train_activations = activations[:split_idx]
    val_activations = activations[split_idx:]

    print(f"Training set shape: {train_activations.shape}")
    print(f"Validation set shape: {val_activations.shape}")

    # Create training configuration
    training_config = TrainingConfig(
        data_dir="",  # Not used in this test
        train_files=[],  # Not used in this test
        output_dir=output_dir,
        save_interval=5,
        eval_interval=2,
    )

    # Update model config
    model.config.batch_size = batch_size
    model.config.num_epochs = num_epochs
    model.config.learning_rate = learning_rate

    # Create trainer
    trainer = SparseAutoencoderTrainer(model, training_config)

    # Train the model
    metrics = trainer.train(
        train_activations=train_activations,
        val_activations=val_activations,
    )

    # Print final metrics
    print(f"\nTraining completed with final metrics:")
    for key, value in metrics.items():
        if isinstance(value, list) and len(value) > 0:
            print(f"  {key}: {value[-1]:.6f}")

    # Plot training curves
    plot_training_curves(metrics, output_dir)

    return metrics


def test_sae_evaluation(model, activations, batch_size=32):
    """Test evaluating the Sparse Autoencoder."""
    print(f"\n=== Testing Sparse Autoencoder evaluation ===")

    # Reshape activations if needed (from [batch, seq_len, hidden] to [batch*seq_len, hidden])
    if len(activations.shape) == 3:
        orig_shape = activations.shape
        activations = activations.reshape(-1, activations.shape[-1])
        print(f"Reshaped activations from {orig_shape} to {activations.shape}")

    # Move to device
    activations = activations.to(model.config.device)

    # Create a small batch
    batch_size = min(batch_size, activations.shape[0])
    batch = activations[:batch_size]

    # Forward pass
    with torch.no_grad():
        reconstructed, latent = model(batch)

    # Compute loss
    loss_dict = model.compute_loss(batch, reconstructed, latent)

    # Print loss components
    print(f"Loss components:")
    for key, value in loss_dict.items():
        print(f"  {key}: {value.item():.6f}")

    # Check for dead neurons
    dead_neurons = model.get_dead_neurons()
    num_dead = torch.sum(dead_neurons).item()
    print(f"  Dead neurons: {num_dead} / {model.latent_dim} ({num_dead / model.latent_dim:.2%})")

    return loss_dict


def plot_training_curves(metrics, output_dir):
    """Plot training curves."""
    print(f"\n=== Plotting training curves ===")

    # Create output directory
    vis_dir = os.path.join(output_dir, "visualizations")
    os.makedirs(vis_dir, exist_ok=True)

    # Plot training and validation losses
    plt.figure(figsize=(10, 6))

    if 'train_losses' in metrics and len(metrics['train_losses']) > 0:
        plt.plot(metrics['train_losses'], label='Train Loss')

    if 'val_losses' in metrics and len(metrics['val_losses']) > 0:
        # Plot at the correct x-coordinates
        eval_interval = len(metrics['train_losses']) // len(metrics['val_losses'])
        x = [i * eval_interval for i in range(len(metrics['val_losses']))]
        plt.plot(x, metrics['val_losses'], label='Validation Loss', marker='o')

    plt.title('Training and Validation Loss')
    plt.xlabel('Epoch')
    plt.ylabel('Loss')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Save figure
    loss_path = os.path.join(vis_dir, "loss_curves.png")
    plt.savefig(loss_path)
    plt.close()
    print(f"Saved loss curves to {loss_path}")

    # Plot sparsity
    plt.figure(figsize=(10, 6))

    if 'train_sparsities' in metrics and len(metrics['train_sparsities']) > 0:
        plt.plot(metrics['train_sparsities'], label='Train Sparsity')

    if 'val_sparsities' in metrics and len(metrics['val_sparsities']) > 0:
        # Plot at the correct x-coordinates
        eval_interval = len(metrics['train_sparsities']) // len(metrics['val_sparsities'])
        x = [i * eval_interval for i in range(len(metrics['val_sparsities']))]
        plt.plot(x, metrics['val_sparsities'], label='Validation Sparsity', marker='o')

    plt.title('Training and Validation Sparsity')
    plt.xlabel('Epoch')
    plt.ylabel('Sparsity')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # Save figure
    sparsity_path = os.path.join(vis_dir, "sparsity_curves.png")
    plt.savefig(sparsity_path)
    plt.close()
    print(f"Saved sparsity curves to {sparsity_path}")

    # Plot dead neurons
    if 'dead_neurons' in metrics and len(metrics['dead_neurons']) > 0:
        plt.figure(figsize=(10, 6))
        plt.plot(metrics['dead_neurons'])
        plt.title('Dead Neurons')
        plt.xlabel('Epoch')
        plt.ylabel('Number of Dead Neurons')
        plt.grid(True, alpha=0.3)

        # Save figure
        dead_path = os.path.join(vis_dir, "dead_neurons.png")
        plt.savefig(dead_path)
        plt.close()
        print(f"Saved dead neurons curve to {dead_path}")


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load or create activations
    if args.activations_file:
        print(f"Loading activations from {args.activations_file}")
        activations = torch.load(args.activations_file, map_location=args.device)
    elif args.create_dummy:
        activations_path = os.path.join(args.output_dir, "dummy_activations.pt")
        activations = create_dummy_activations(activations_path, args.model, device=args.device)
    else:
        raise ValueError("Either provide an activations file with --activations_file or use --create_dummy to create dummy activations")

    # Ensure activations are on the correct device
    activations = activations.to(args.device)
    print(f"Moved activations to device: {activations.device} with dtype: {activations.dtype}")

    # Print data type information
    print(f"Activations data type: {activations.dtype}")

    # Get input dimension from activations
    input_dim = activations.shape[-1]
    print(f"Activation shape: {activations.shape}, input_dim: {input_dim}")

    # Get model embedding size
    embedding_size = get_model_embedding_size(args.model)
    print(f"Model embedding size: {embedding_size}")

    # Use 2x embedding size for latent dimension if not specified
    latent_dim = args.latent_dim or (embedding_size * 2)
    print(f"Using latent dimension: {latent_dim}")

    # Test SAE creation
    model = test_sae_creation(
        input_dim=input_dim,
        latent_dim=latent_dim,
        hidden_dim=input_dim,  # Use same dimension as input for hidden layer
        l1_coefficient=args.l1_coefficient,
        topk=args.topk,
        device=args.device,
    )

    # Test SAE forward pass
    reconstructed, latent = test_sae_forward(
        model=model,
        activations=activations,
        batch_size=args.batch_size,
    )

    # Test SAE training
    metrics = test_sae_training(
        model=model,
        activations=activations,
        output_dir=args.output_dir,
        batch_size=args.batch_size,
        num_epochs=args.num_epochs,
        learning_rate=args.learning_rate,
    )

    # Test SAE evaluation
    loss_dict = test_sae_evaluation(
        model=model,
        activations=activations,
        batch_size=args.batch_size,
    )

    # Save the model
    model_path = os.path.join(args.output_dir, "sae_model.pt")
    model.save(model_path)
    print(f"\nSaved model to {model_path}")

    print("\n=== All tests completed successfully! ===")


if __name__ == "__main__":
    main()

#!/usr/bin/env python
"""
Run all GOSAE tests in sequence.

This script runs all the main test scripts for the GOSAE framework in the correct order,
respecting dependencies between tests.
"""

import os
import argparse
import subprocess
import sys
from typing import List


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments.

    Returns:
        Parsed command-line arguments.
    """
    parser = argparse.ArgumentParser(
        description="Run all GOSAE tests in sequence.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default="test_outputs",
        help="Output directory for test results."
    )
    parser.add_argument(
        "--model",
        type=str,
        default="pGenomeOcean/GenomeOcean-100M",
        help="GenomeOcean model name or path."
    )
    # No longer using --create_sample flag as we're using real data
    parser.add_argument(
        "--data_dir",
        type=str,
        default="test_data/zymo",
        help="Directory containing genomic data for testing."
    )
    parser.add_argument(
        "--genome_file",
        type=str,
        default="Staphylococcus_aureus_complete_genome.fasta",
        help="Genome file to use for testing."
    )

    return parser.parse_args()


def run_test(test_script: str, args: argparse.Namespace, extra_args: List[str] = None) -> bool:
    """Run a test script.

    Args:
        test_script: Path to the test script.
        args: Command-line arguments.
        extra_args: Additional arguments to pass to the test script.

    Returns:
        True if the test was successful, False otherwise.
    """
    print(f"\n{'='*80}")
    print(f"Running test: {test_script}")
    print(f"{'='*80}\n")

    # Create test-specific output directory
    test_name = os.path.splitext(os.path.basename(test_script))[0]
    test_output_dir = os.path.join(args.output_dir, test_name)
    os.makedirs(test_output_dir, exist_ok=True)

    # Build command
    cmd = ["python", test_script, "--model", args.model]

    if extra_args:
        cmd.extend(extra_args)

    # Run the command
    try:
        process = subprocess.run(cmd, check=False, capture_output=True, text=True)

        # Print output
        if process.stdout:
            print(process.stdout)

        # Check if the test was successful
        if process.returncode == 0:
            print(f"\n{'='*80}")
            print(f"Test {test_script} completed successfully!")
            print(f"{'='*80}\n")
            return True
        else:
            print(f"\n{'='*80}")
            print(f"Test {test_script} failed with return code {process.returncode}")
            if process.stderr:
                print("\nError output:")
                print(process.stderr)
            print(f"{'='*80}\n")
            return False
    except Exception as e:
        print(f"\n{'='*80}")
        print(f"Error running test {test_script}: {e}")
        print(f"{'='*80}\n")
        return False


def main() -> None:
    """Main function.

    Parses command-line arguments and runs all tests in sequence.
    """
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Define test scripts and their dependencies
    # Create output paths for intermediate files
    genomic_data_output = os.path.join(args.output_dir, "test_genomic_data")
    activation_output = os.path.join(args.output_dir, "test_activation_extraction")
    sae_output = os.path.join(args.output_dir, "test_sae_training")
    visualization_output = os.path.join(args.output_dir, "test_visualization")
    concept_output = os.path.join(args.output_dir, "test_concept_classification")

    # Create directories
    for dir_path in [genomic_data_output, activation_output, sae_output, visualization_output, concept_output]:
        os.makedirs(dir_path, exist_ok=True)

    # Define paths for intermediate files
    data_file = os.path.join(args.data_dir, args.genome_file)
    activations_file = os.path.join(activation_output, "activations.pt")
    model_file = os.path.join(sae_output, "sae_model.pt")

    test_scripts = [
        {
            "name": "genomic_data",
            "script": "tests/test_genomic_data.py",
            "description": "Genomic data processing",
            "extra_args": [
                "--data_file", data_file,
                "--output_dir", genomic_data_output
            ]
        },
        {
            "name": "activation_extraction",
            "script": "tests/test_activation_extraction.py",
            "description": "Activation extraction from GenomeOcean model",
            "extra_args": [
                "--data_file", data_file,
                "--output_dir", activation_output
            ]
        },
        {
            "name": "sae_training",
            "script": "tests/test_sae_training.py",
            "description": "Sparse Autoencoder training",
            "extra_args": [
                "--activations_file", activations_file,
                "--output_dir", sae_output
            ]
        },
        {
            "name": "visualization",
            "script": "tests/test_visualization.py",
            "description": "Feature visualization",
            "extra_args": [
                "--model_file", model_file,
                "--activations_file", activations_file,
                "--output_dir", visualization_output
            ]
        },
        {
            "name": "concept_classification",
            "script": "tests/test_concept_classification.py",
            "description": "Concept classification using SAE features",
            "extra_args": [
                "--model_file", model_file,
                "--create_dummy",
                "--output_dir", concept_output
            ]
        }
    ]

    # Print test plan
    print("\n" + "="*80)
    print("Test Plan")
    print("="*80)
    print(f"Tests to run: {len(test_scripts)}")
    for i, test in enumerate(test_scripts):
        print(f"  {i+1}. {test['name']}: {test['description']}")
    print("="*80 + "\n")

    # Track successful tests
    successful_tests = []
    failed_tests = []

    # Run tests
    for test in test_scripts:
        success = run_test(
            test["script"],
            args,
            test.get("extra_args", [])
        )

        if success:
            successful_tests.append(test["name"])
        else:
            failed_tests.append(test["name"])
            # Don't stop on failure, continue with next test

    # Print summary
    print("\n" + "="*80)
    print("Test Summary")
    print("="*80)
    print(f"Total tests: {len(test_scripts)}")
    print(f"Successful tests: {len(successful_tests)}")
    print(f"Failed tests: {len(failed_tests)}")

    if failed_tests:
        print("\nFailed tests:")
        for i, test_name in enumerate(failed_tests):
            test = next((t for t in test_scripts if t["name"] == test_name), None)
            if test:
                print(f"  {i+1}. {test_name}: {test['description']}")

    print("\n" + "="*80)

    # Exit with error code if any tests failed
    if failed_tests:
        sys.exit(1)
    else:
        sys.exit(0)


if __name__ == "__main__":
    main()

#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to run all GOSAE tests.

This script runs all the test scripts for the GOSAE framework in the correct order,
respecting dependencies between tests.
"""

import os
import argparse
import subprocess
import sys
from typing import Dict, List, Set


def parse_args() -> argparse.Namespace:
    """Parse command-line arguments.

    Returns:
        Parsed command-line arguments.
    """
    parser = argparse.ArgumentParser(
        description="Run GOSAE tests in the correct order, respecting dependencies.",
        formatter_class=argparse.ArgumentDefaultsHelpFormatter
    )

    parser.add_argument(
        "--output_dir",
        type=str,
        default="test_outputs",
        help="Output directory for test results."
    )
    parser.add_argument(
        "--model",
        type=str,
        default="pGenomeOcean/GenomeOcean-100M",
        help="GenomeOcean model name or path."
    )
    parser.add_argument(
        "--create_sample",
        action="store_true",
        help="Create sample data for tests."
    )
    parser.add_argument(
        "--tests",
        type=str,
        nargs="+",
        default=["all"],
        choices=[
            "all",
            "genomic_data",
            "activation_extraction",
            "sae_training",
            "visualization",
            "concept_classification"
        ],
        help="Tests to run."
    )

    return parser.parse_args()


def run_test(test_script: str, args: argparse.Namespace, output_dir: str) -> bool:
    """Run a test script.

    Args:
        test_script: Path to the test script.
        args: Command-line arguments.
        output_dir: Output directory for test results.

    Returns:
        True if the test was successful, False otherwise.
    """
    print(f"\n{'='*80}")
    print(f"Running test: {test_script}")
    print(f"{'='*80}\n")

    # Create test-specific output directory
    test_name = os.path.splitext(os.path.basename(test_script))[0]
    test_output_dir = os.path.join(output_dir, test_name)
    os.makedirs(test_output_dir, exist_ok=True)

    # Build command
    cmd = ["python", test_script, "--output_dir", test_output_dir, "--model", args.model]

    if args.create_sample:
        cmd.append("--create_sample")

    # Run the command
    try:
        process = subprocess.run(cmd, check=False, capture_output=True, text=True)

        # Print output
        if process.stdout:
            print(process.stdout)

        # Check if the test was successful
        if process.returncode == 0:
            print(f"\n{'='*80}")
            print(f"Test {test_script} completed successfully!")
            print(f"{'='*80}\n")
            return True
        else:
            print(f"\n{'='*80}")
            print(f"Test {test_script} failed with return code {process.returncode}")
            if process.stderr:
                print("\nError output:")
                print(process.stderr)
            print(f"{'='*80}\n")
            return False
    except Exception as e:
        print(f"\n{'='*80}")
        print(f"Error running test {test_script}: {e}")
        print(f"{'='*80}\n")
        return False


def run_tests_with_dependencies(args: argparse.Namespace) -> None:
    """Run tests with dependencies.

    Args:
        args: Command-line arguments.
    """
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Define test scripts and their dependencies
    test_scripts: Dict[str, Dict[str, List[str] | str]] = {
        "genomic_data": {
            "script": "tests/test_genomic_data.py",
            "depends_on": [],
            "description": "Genomic data processing",
        },
        "activation_extraction": {
            "script": "tests/test_activation_extraction.py",
            "depends_on": ["genomic_data"],
            "description": "Activation extraction from GenomeOcean model",
        },
        "sae_training": {
            "script": "tests/test_sae_training.py",
            "depends_on": ["activation_extraction"],
            "description": "Sparse Autoencoder training",
        },
        "visualization": {
            "script": "tests/test_visualization.py",
            "depends_on": ["sae_training"],
            "description": "Feature visualization",
        },
        "concept_classification": {
            "script": "tests/test_concept_classification.py",
            "depends_on": ["sae_training"],
            "description": "Concept classification using SAE features",
        },
    }

    # Determine which tests to run
    tests_to_run = list(test_scripts.keys()) if "all" in args.tests else args.tests

    # Print test plan
    print("\n" + "="*80)
    print("Test Plan")
    print("="*80)
    print(f"Tests to run: {len(tests_to_run)}")
    for test in tests_to_run:
        deps = test_scripts[test]["depends_on"]
        desc = test_scripts[test]["description"]
        print(f"  - {test}: {desc}")
        if deps:
            print(f"    Dependencies: {', '.join(deps)}")
    print("="*80 + "\n")

    # Track successful tests
    successful_tests: Set[str] = set()

    # Run tests
    for test in tests_to_run:
        # Check if dependencies have been run successfully
        deps = test_scripts[test]["depends_on"]
        missing_deps = [dep for dep in deps if dep not in successful_tests]

        if missing_deps:
            print(f"\nSkipping test {test} because dependencies {missing_deps} have not been run successfully")
            continue

        # Run the test
        success = run_test(test_scripts[test]["script"], args, args.output_dir)

        if success:
            successful_tests.add(test)

    # Print summary
    print("\n" + "="*80)
    print("Test Summary")
    print("="*80)
    print(f"Total tests: {len(tests_to_run)}")
    print(f"Successful tests: {len(successful_tests)}")
    print(f"Failed tests: {len(tests_to_run) - len(successful_tests)}")

    if len(successful_tests) < len(tests_to_run):
        print("\nFailed tests:")
        for test in tests_to_run:
            if test not in successful_tests:
                print(f"  - {test}: {test_scripts[test]['description']}")

    print("\n" + "="*80)

    # Exit with error code if any tests failed
    if len(successful_tests) < len(tests_to_run):
        sys.exit(1)


def main() -> None:
    """Main function.

    Parses command-line arguments and runs tests with dependencies.
    """
    # Parse arguments
    args = parse_args()

    # Run tests
    run_tests_with_dependencies(args)

    # Exit with success
    sys.exit(0)


if __name__ == "__main__":
    main()

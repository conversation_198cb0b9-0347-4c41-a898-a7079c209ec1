#!/usr/bin/env python
"""
Test script for genomic data processing.

This script tests the genomic data processing functionality of the GOSAE framework,
including loading FASTA files, processing sequences, and tokenizing DNA.
"""

import os
import argparse
import torch
from pathlib import Path

from gosae.data.genomic_data import GenomicDataProcessor
from gosae.data.dna_tokenizer import DNATokenizer
from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_token_limit


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Test genomic data processing.")

    parser.add_argument("--data_file", type=str, help="Path to genomic data file (FASTA or GenBank).")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name or path.")
    parser.add_argument("--output_dir", type=str, default="test_outputs", help="Output directory.")
    parser.add_argument("--create_sample", action="store_true", help="Create a sample FASTA file if no data file is provided.")

    return parser.parse_args()


def create_sample_fasta(output_path):
    """Create a sample FASTA file for testing."""
    print(f"Creating sample FASTA file at {output_path}")

    # Create some sample sequences
    sequences = [
        (">sample1", "ATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGC"),
        (">sample2", "GGGCCCAAATTTGGGCCCAAATTTGGGCCCAAATTTGGGCCCAAATTTGGGCCCAAATTT"),
        (">sample3", "NNNNNATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCATGCNNNNN"),
        (">sample4", "ATATATATATATATATATATATATATATATATATATATATATATATATATATATATATAT"),
        (">sample5", "GCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGCGC"),
    ]

    # Write to file
    os.makedirs(os.path.dirname(output_path), exist_ok=True)
    with open(output_path, "w") as f:
        for header, seq in sequences:
            f.write(f"{header}\n{seq}\n")

    return output_path


def test_load_fasta(data_processor, fasta_path):
    """Test loading sequences from a FASTA file."""
    print(f"\n=== Testing loading FASTA file: {fasta_path} ===")

    # Load sequences
    seq_records = data_processor.load_fasta(fasta_path)

    # Print information
    print(f"Loaded {len(seq_records)} sequences")
    for i, record in enumerate(seq_records):
        print(f"  Sequence {i+1}: {record.id}, Length: {len(record.seq)}")

    return seq_records


def test_process_sequences(data_processor, seq_records):
    """Test processing sequences."""
    print("\n=== Testing sequence processing ===")

    # Process sequences
    processed_sequences = data_processor.process_sequences(seq_records)

    # Print information
    print(f"Processed {len(processed_sequences)} sequences")
    for i, seq in enumerate(processed_sequences):
        print(f"  Sequence {i+1}: Length: {len(seq)}, First 20 bp: {seq[:20]}...")

    # Get sequence statistics
    stats = data_processor.get_sequence_stats(processed_sequences)

    # Print statistics
    print("\nSequence Statistics:")
    print(f"  Number of sequences: {stats['num_sequences']}")
    print(f"  Total length: {stats['total_length']} bp")
    print(f"  Min length: {stats['min_length']} bp")
    print(f"  Max length: {stats['max_length']} bp")
    print(f"  Mean length: {stats['mean_length']:.2f} bp")
    print(f"  Median length: {stats['median_length']:.2f} bp")

    print("\nNucleotide Frequencies:")
    for nucleotide, freq in stats['nucleotide_frequencies'].items():
        print(f"  {nucleotide}: {freq:.4f}")

    return processed_sequences


def test_tokenize_sequences(data_processor, sequences):
    """Test tokenizing sequences."""
    print("\n=== Testing sequence tokenization ===")

    try:
        # Try tokenizing all sequences at once
        print(f"Tokenizing {len(sequences)} sequences at once...")
        tokenized = data_processor.tokenize_sequences(
            sequences=sequences,
            batch_size=len(sequences),  # Process all at once
            return_tensors="pt",
            padding="max_length",  # Use max_length padding
            truncation=True,
        )
    except Exception as e:
        print(f"Error tokenizing all sequences at once: {e}")
        print("Trying with smaller batch size...")

        # Try with smaller batch size
        tokenized = data_processor.tokenize_sequences(
            sequences=sequences,
            batch_size=2,  # Small batch size
            return_tensors="pt",
            padding="max_length",  # Use max_length padding
            truncation=True,
        )

    # Print information
    print(f"Successfully tokenized {len(sequences)} sequences")
    print(f"Input IDs shape: {tokenized['input_ids'].shape}")
    print(f"Attention mask shape: {tokenized['attention_mask'].shape}")

    # Print first sequence tokens
    print("\nFirst sequence tokens:")
    first_seq_tokens = tokenized['input_ids'][0]
    print(f"  Token IDs: {first_seq_tokens[:10]}...")

    # Decode first sequence
    try:
        decoded = data_processor.tokenizer.decode(first_seq_tokens)
        print(f"  Decoded: {decoded[:50]}...")
    except Exception as e:
        print(f"Error decoding tokens: {e}")

    return tokenized


def test_prepare_for_model(data_processor, sequences):
    """Test preparing sequences for the model."""
    print("\n=== Testing preparing sequences for model ===")

    try:
        # Try preparing all sequences at once
        print(f"Preparing {len(sequences)} sequences for model...")
        dataloader = data_processor.prepare_for_model(
            sequences=sequences,
            batch_size=min(8, len(sequences)),  # Use a reasonable batch size
        )

        # Print information
        print(f"Created DataLoader with {len(dataloader)} batches")

        # Iterate through batches
        for i, batch in enumerate(dataloader):
            try:
                input_ids, attention_mask = batch
                print(f"  Batch {i+1}: Input IDs shape: {input_ids.shape}, Attention mask shape: {attention_mask.shape}")
                if i >= 2:  # Only show first few batches
                    print("  ...")
                    break
            except Exception as e:
                print(f"  Error processing batch {i+1}: {e}")
                continue
    except Exception as e:
        print(f"Error preparing sequences for model: {e}")
        print("Trying with a smaller subset of sequences...")

        # Try with a smaller subset
        subset_size = min(5, len(sequences))
        subset_sequences = sequences[:subset_size]
        print(f"Using {subset_size} sequences...")

        dataloader = data_processor.prepare_for_model(
            sequences=subset_sequences,
            batch_size=1,  # Use smallest possible batch size
        )

        print(f"Created DataLoader with {len(dataloader)} batches")

        # Iterate through batches
        for i, batch in enumerate(dataloader):
            try:
                input_ids, attention_mask = batch
                print(f"  Batch {i+1}: Input IDs shape: {input_ids.shape}, Attention mask shape: {attention_mask.shape}")
            except Exception as e:
                print(f"  Error processing batch {i+1}: {e}")
                continue

    return dataloader


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Create or use data file
    if args.data_file:
        data_file = args.data_file
    elif args.create_sample:
        data_file = create_sample_fasta(os.path.join(args.output_dir, "sample.fa"))
    else:
        raise ValueError("Either provide a data file with --data_file or use --create_sample to create a sample file")

    # Initialize data processor
    print(f"\n=== Initializing GenomicDataProcessor for model: {args.model} ===")
    try:
        data_processor = GenomicDataProcessor(
            tokenizer_model=args.model,
        )
    except Exception as e:
        print(f"Error initializing GenomicDataProcessor: {e}")
        print("Trying with default model settings...")
        data_processor = GenomicDataProcessor()

    # Track successful tests
    successful_tests = []

    # Test loading FASTA
    try:
        seq_records = test_load_fasta(data_processor, data_file)
        successful_tests.append("load_fasta")
    except Exception as e:
        print(f"Error in test_load_fasta: {e}")
        print("Skipping remaining tests that depend on FASTA loading")
        return

    # Test processing sequences
    try:
        processed_sequences = test_process_sequences(data_processor, seq_records)
        successful_tests.append("process_sequences")
    except Exception as e:
        print(f"Error in test_process_sequences: {e}")
        print("Skipping remaining tests that depend on sequence processing")
        return

    # Test tokenizing sequences
    try:
        tokenized = test_tokenize_sequences(data_processor, processed_sequences)
        successful_tests.append("tokenize_sequences")
    except Exception as e:
        print(f"Error in test_tokenize_sequences: {e}")
        print("Continuing with remaining tests...")

    # Test preparing for model
    try:
        dataloader = test_prepare_for_model(data_processor, processed_sequences)
        successful_tests.append("prepare_for_model")
    except Exception as e:
        print(f"Error in test_prepare_for_model: {e}")

    # Print summary
    print("\n=== Test Summary ===")
    print(f"Total tests: 4")
    print(f"Successful tests: {len(successful_tests)}")

    if len(successful_tests) == 4:
        print("\n=== All tests completed successfully! ===")
    else:
        print("\n=== Some tests failed ===")
        print("Successful tests:")
        for test in successful_tests:
            print(f"  - {test}")

        print("Failed tests:")
        all_tests = ["load_fasta", "process_sequences", "tokenize_sequences", "prepare_for_model"]
        for test in all_tests:
            if test not in successful_tests:
                print(f"  - {test}")


if __name__ == "__main__":
    main()

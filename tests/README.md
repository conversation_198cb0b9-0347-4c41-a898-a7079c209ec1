# GOSAE Tests

This directory contains test scripts for the GenomeOcean Sparse Autoencoder (GOSAE) framework.

## Available Tests

1. **All Tests** (`test_all.py`): Runs all tests in sequence.
2. **Genomic Data Processing** (`test_genomic_data.py`): Tests loading, processing, and tokenizing genomic data.
3. **Activation Extraction** (`test_activation_extraction.py`): Tests extracting activations from the GenomeOcean model.
4. **SAE Training** (`test_sae_training.py`): Tests training a Sparse Autoencoder on activations.
5. **Visualization** (`test_visualization.py`): Tests visualizing features discovered by a Sparse Autoencoder.
6. **Concept Classification** (`test_concept_classification.py`): Tests classifying genomic concepts using Sparse Autoencoder features.

## Running Tests

You can run all tests or individual tests using the provided scripts.

### Running All Tests

From the project root directory:

```bash
python run_tests.py --create_sample
```

Or directly from the tests directory:

```bash
python test_all.py --create_sample
```

This will run all tests in sequence, creating sample data as needed.

### Test Options

- `--output_dir`: Directory to store test outputs (default: `test_outputs`)
- `--model`: GenomeOcean model to use (default: `pGenomeOcean/GenomeOcean-100M`)
- `--create_sample`: Create sample data for tests
- `--data_dir`: Directory containing genomic data for testing (default: `test_data/zymo`)
- `--genome_file`: Genome file to use for testing (default: `Staphylococcus_aureus_complete_genome.fasta`)

## Running Individual Tests

You can also run individual test scripts directly:

### 1. Genomic Data Processing

```bash
python test_genomic_data.py --create_sample --data_dir ../test_data/zymo --genome_file Staphylococcus_aureus_complete_genome.fasta
```

### 2. Activation Extraction

```bash
python test_activation_extraction.py --create_sample --data_dir ../test_data/zymo --genome_file Staphylococcus_aureus_complete_genome.fasta
```

### 3. SAE Training

```bash
python test_sae_training.py --create_dummy
```

### 4. Visualization

```bash
python test_visualization.py --create_dummy
```

### 5. Concept Classification

```bash
python test_concept_classification.py --create_dummy
```

## Test Dependencies

The tests have the following dependencies:

- `test_genomic_data.py`: No dependencies
- `test_activation_extraction.py`: Depends on genomic data
- `test_sae_training.py`: Depends on activation extraction
- `test_visualization.py`: Depends on SAE training
- `test_concept_classification.py`: Depends on SAE training

When running the `test_all.py` script, these dependencies are respected by running the tests in the correct order.

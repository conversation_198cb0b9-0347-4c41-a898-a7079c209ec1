#!/usr/bin/env python
"""
Inspect the GenomeOcean model to print its layer structure and dimensionalities.
"""

import torch
from transformers import AutoModel, AutoTokenizer

def inspect_model(model_name: str):
    """
    Loads a model and prints its layer structure and parameter dimensionalities.

    Args:
        model_name (str): The name of the model to inspect (e.g., 'pGenomeOcean/GenomeOcean-100M').
    """
    print(f"Loading model: {model_name}")
    try:
        model = AutoModel.from_pretrained(model_name)
        tokenizer = AutoTokenizer.from_pretrained(model_name)
    except Exception as e:
        print(f"Error loading model: {e}")
        return

    print("\nModel Architecture:\n")
    print(model)

    print("\nParameter Dimensionalities:\n")
    for name, param in model.named_parameters():
        print(f"{name:<60} | Shape: {str(list(param.shape)):<20} | Num elements: {param.numel()}")

    print(f"\nTokenizer vocabulary size: {tokenizer.vocab_size}")


if __name__ == "__main__":
    # You can change this to any other GenomeOcean model
    default_model_name = "pGenomeOcean/GenomeOcean-100M"
    inspect_model(default_model_name)

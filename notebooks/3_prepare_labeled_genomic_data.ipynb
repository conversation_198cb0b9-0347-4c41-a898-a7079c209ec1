{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Preparing Labeled Genomic Data for Concept Mapping\n", "\n", "This notebook demonstrates how to prepare genomic data with known labels for mapping genomic concepts to Sparse Autoencoder features. We'll cover:\n", "\n", "1. Loading genomic data with known annotations\n", "2. Extracting specific genomic features (e.g., genes, promoters, etc.)\n", "3. Creating labeled datasets for concept mapping\n", "4. Extracting activations for labeled sequences\n", "5. Saving the labeled data for later use\n", "\n", "## Setup\n", "\n", "First, let's import the necessary libraries and set up the environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import torch\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "from Bio import SeqIO\n", "from Bio.SeqRecord import SeqRecord\n", "from Bio.Seq import Seq\n", "\n", "# Import GOSAE components\n", "from gosae.data.genomic_data import GenomicDataProcessor\n", "from gosae.model.activation_extractor import GenomeOceanActivationExtractor\n", "from gosae.utils.config import GenomeOceanConfig\n", "from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_token_limit\n", "\n", "# Set random seed for reproducibility\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration\n", "\n", "Let's set up the configuration for our data processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "config = {\n", "    \"data_dir\": \"../test_data/zymo\",  # Directory containing genomic data\n", "    \"output_dir\": \"../data/labeled\",  # Directory to save labeled data\n", "    \"model_name\": DEFAULT_MODEL,  # GenomeOcean model name\n", "    \"target_layer\": \"model.layers.8.mlp.up_proj\",  # Target layer for activation extraction\n", "    \"max_length\": get_model_token_limit(DEFAULT_MODEL),  # Maximum token length\n", "    \"batch_size\": 8,  # Batch size for processing\n", "    \"device\": \"cuda\" if torch.cuda.is_available() else \"cpu\",  # Device to use\n", "    \"window_size\": 512,  # Window size for sequence extraction\n", "    \"context_size\": 128  # Context size around features\n", "}\n", "\n", "# Create output directory if it doesn't exist\n", "os.makedirs(config[\"output_dir\"], exist_ok=True)\n", "\n", "print(f\"Using device: {config['device']}\")\n", "print(f\"Model: {config['model_name']}\")\n", "print(f\"Target layer: {config['target_layer']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Define Genomic Feature Classes\n", "\n", "Let's define the genomic feature classes that we'll use for concept mapping."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Define genomic feature classes\n", "feature_classes = {\n", "    0: \"Coding Sequence (CDS)\",\n", "    1: \"Promoter Region\",\n", "    2: \"Terminator Region\",\n", "    3: \"Intergenic Region\",\n", "    4: \"Ribosome Binding Site (RBS)\",\n", "    5: \"tRNA Gene\",\n", "    6: \"rRNA Gene\",\n", "    7: \"Repeat Region\",\n", "    8: \"Origin of Replication\"\n", "}\n", "\n", "# Print feature classes\n", "print(\"Genomic Feature Classes:\")\n", "for class_id, class_name in feature_classes.items():\n", "    print(f\"  {class_id}: {class_name}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Genomic Data\n", "\n", "Let's load the genomic data from GenBank files, which contain annotations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Function to load GenBank files\n", "def load_genbank_files(data_dir):\n", "    genbank_files = [f for f in os.listdir(data_dir) if f.endswith(\".gb\") or f.endswith(\".gbk\")]\n", "    \n", "    if not genbank_files:\n", "        print(\"No GenBank files found. Using FASTA files instead.\")\n", "        # For demonstration purposes, we'll create synthetic annotations for FASTA files\n", "        return load_fasta_with_synthetic_annotations(data_dir)\n", "    \n", "    records = []\n", "    for file in genbank_files:\n", "        file_path = os.path.join(data_dir, file)\n", "        records.extend(list(SeqIO.parse(file_path, \"genbank\")))\n", "    \n", "    return records\n", "\n", "# Function to load FASTA files and create synthetic annotations\n", "def load_fasta_with_synthetic_annotations(data_dir):\n", "    fasta_files = [f for f in os.listdir(data_dir) if f.endswith(\".fasta\") or f.endswith(\".fa\")]\n", "    \n", "    if not fasta_files:\n", "        raise ValueError(f\"No FASTA files found in {data_dir}\")\n", "    \n", "    # Load sequences\n", "    sequences = []\n", "    for file in fasta_files[:3]:  # Limit to 3 files for demonstration\n", "        file_path = os.path.join(data_dir, file)\n", "        sequences.extend(list(SeqIO.parse(file_path, \"fasta\")))\n", "    \n", "    # Create synthetic annotations\n", "    annotated_records = []\n", "    for seq_record in sequences:\n", "        # Create a new record with the same sequence\n", "        record = SeqRecord(\n", "            seq=seq_record.seq,\n", "            id=seq_record.id,\n", "            name=seq_record.name,\n", "            description=seq_record.description,\n", "            annotations={\"molecule_type\": \"DNA\"}\n", "        )\n", "        \n", "        # Add synthetic features\n", "        from Bio.SeqFeature import SeqFeature, FeatureLocation\n", "        \n", "        seq_length = len(record.seq)\n", "        \n", "        # Add CDS features (every 2000 bp)\n", "        for start in range(0, seq_length - 1000, 2000):\n", "            end = min(start + 1000, seq_length)\n", "            feature = SeqFeature(\n", "                FeatureLocation(start, end),\n", "                type=\"CDS\",\n", "                qualifiers={\"gene\": [f\"gene_{start}\"]}\n", "            )\n", "            record.features.append(feature)\n", "        \n", "        # Add promoter features (before each CDS)\n", "        for start in range(0, seq_length - 1000, 2000):\n", "            promoter_start = max(0, start - 200)\n", "            promoter_end = start\n", "            if promoter_start < promoter_end:\n", "                feature = SeqFeature(\n", "                    FeatureLocation(promoter_start, promoter_end),\n", "                    type=\"promoter\",\n", "                    qualifiers={\"note\": [f\"promoter_{start}\"]}\n", "                )\n", "                record.features.append(feature)\n", "        \n", "        # Add terminator features (after each CDS)\n", "        for start in range(0, seq_length - 1000, 2000):\n", "            end = min(start + 1000, seq_length)\n", "            terminator_start = end\n", "            terminator_end = min(end + 100, seq_length)\n", "            if terminator_start < terminator_end:\n", "                feature = SeqFeature(\n", "                    FeatureLocation(terminator_start, terminator_end),\n", "                    type=\"terminator\",\n", "                    qualifiers={\"note\": [f\"terminator_{start}\"]}\n", "                )\n", "                record.features.append(feature)\n", "        \n", "        # Add tRNA features\n", "        for start in range(500, seq_length - 100, 5000):\n", "            end = min(start + 80, seq_length)\n", "            feature = SeqFeature(\n", "                FeatureLocation(start, end),\n", "                type=\"tRNA\",\n", "                qualifiers={\"note\": [f\"tRNA_{start}\"]}\n", "            )\n", "            record.features.append(feature)\n", "        \n", "        # Add rRNA features\n", "        for start in range(10000, seq_length - 1000, 20000):\n", "            end = min(start + 500, seq_length)\n", "            feature = SeqFeature(\n", "                FeatureLocation(start, end),\n", "                type=\"rRNA\",\n", "                qualifiers={\"note\": [f\"rRNA_{start}\"]}\n", "            )\n", "            record.features.append(feature)\n", "        \n", "        annotated_records.append(record)\n", "    \n", "    return annotated_records\n", "\n", "# Load genomic data\n", "print(\"Loading genomic data...\")\n", "records = load_genbank_files(config[\"data_dir\"])\n", "print(f\"Loaded {len(records)} genomic records\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Labeled Sequences\n", "\n", "Now, let's extract labeled sequences from the genomic records."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Function to extract labeled sequences\n", "def extract_labeled_sequences(records, window_size=512, context_size=128):\n", "    labeled_sequences = []\n", "    labels = []\n", "    \n", "    for record in records:\n", "        seq_str = str(record.seq)\n", "        seq_length = len(seq_str)\n", "        \n", "        # Process each feature\n", "        for feature in record.features:\n", "            # Skip features without a type\n", "            if not hasattr(feature, \"type\"):\n", "                continue\n", "            \n", "            # Map feature type to class ID\n", "            feature_type = feature.type.lower()\n", "            class_id = None\n", "            \n", "            if feature_type == \"cds\":\n", "                class_id = 0\n", "            elif feature_type == \"promoter\":\n", "                class_id = 1\n", "            elif feature_type == \"terminator\":\n", "                class_id = 2\n", "            elif feature_type == \"intergenic\":\n", "                class_id = 3\n", "            elif feature_type == \"rbs\" or \"ribosome_binding_site\" in feature_type:\n", "                class_id = 4\n", "            elif feature_type == \"trna\":\n", "                class_id = 5\n", "            elif feature_type == \"rrna\":\n", "                class_id = 6\n", "            elif \"repeat\" in feature_type:\n", "                class_id = 7\n", "            elif \"origin\" in feature_type or \"ori\" == feature_type:\n", "                class_id = 8\n", "            \n", "            # Skip features without a mapped class\n", "            if class_id is None:\n", "                continue\n", "            \n", "            # Get feature location\n", "            start = int(feature.location.start)\n", "            end = int(feature.location.end)\n", "            \n", "            # Skip features that are too short\n", "            if end - start < 20:\n", "                continue\n", "            \n", "            # Extract sequence with context\n", "            context_start = max(0, start - context_size)\n", "            context_end = min(seq_length, end + context_size)\n", "            \n", "            # Skip if the context is too short\n", "            if context_end - context_start < window_size:\n", "                continue\n", "            \n", "            # Extract windows from the feature with context\n", "            for window_start in range(context_start, context_end - window_size + 1, window_size // 2):\n", "                window_end = window_start + window_size\n", "                if window_end > context_end:\n", "                    break\n", "                \n", "                # Extract window\n", "                window_seq = seq_str[window_start:window_end]\n", "                \n", "                # Add to labeled sequences\n", "                labeled_sequences.append(window_seq)\n", "                labels.append(class_id)\n", "    \n", "    return labeled_sequences, labels\n", "\n", "# Extract labeled sequences\n", "print(\"Extracting labeled sequences...\")\n", "labeled_sequences, labels = extract_labeled_sequences(\n", "    records,\n", "    window_size=config[\"window_size\"],\n", "    context_size=config[\"context_size\"]\n", ")\n", "print(f\"Extracted {len(labeled_sequences)} labeled sequences\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Label Distribution\n", "\n", "Let's analyze the distribution of labels in our dataset."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Count labels\n", "label_counts = {}\n", "for label in labels:\n", "    label_counts[label] = label_counts.get(label, 0) + 1\n", "\n", "# Print label counts\n", "print(\"Label Distribution:\")\n", "for label, count in sorted(label_counts.items()):\n", "    print(f\"  {label} ({feature_classes[label]}): {count} sequences\")\n", "\n", "# Plot label distribution\n", "plt.figure(figsize=(12, 6))\n", "plt.bar(\n", "    [feature_classes[label] for label in sorted(label_counts.keys())],\n", "    [label_counts[label] for label in sorted(label_counts.keys())]\n", ")\n", "plt.title(\"Label Distribution\")\n", "plt.xlabel(\"Feature Class\")\n", "plt.ylabel(\"Count\")\n", "plt.xticks(rotation=45, ha=\"right\")\n", "plt.tight_layout()\n", "plt.grid(axis=\"y\", alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tokenize Labeled Sequences\n", "\n", "Now, let's tokenize the labeled sequences using the DNATokenizer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize data processor\n", "data_processor = GenomicDataProcessor(\n", "    max_length=config[\"max_length\"],\n", "    overlap=0,\n", "    random_crop=False,\n", "    tokenizer_model=config[\"model_name\"]\n", ")\n", "\n", "# Tokenize sequences\n", "print(\"Tokenizing sequences...\")\n", "tokenized = data_processor.tokenize_sequences(\n", "    sequences=labeled_sequences,\n", "    batch_size=config[\"batch_size\"],\n", "    return_tensors=\"pt\",\n", "    padding=True,\n", "    truncation=True\n", ")\n", "\n", "print(f\"Tokenized {len(labeled_sequences)} sequences\")\n", "print(f\"Input IDs shape: {tokenized['input_ids'].shape}\")\n", "print(f\"Attention mask shape: {tokenized['attention_mask'].shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Activations\n", "\n", "Now, let's extract activations from the GenomeOcean model using the tokenized sequences."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize GenomeOcean activation extractor\n", "genomeocean_config = GenomeOceanConfig(\n", "    model_name=config[\"model_name\"],\n", "    target_layers=[config[\"target_layer\"]],\n", "    cache_dir=os.path.join(config[\"output_dir\"], \"cache\"),\n", "    use_cache=True\n", ")\n", "\n", "print(\"Initializing GenomeOceanActivationExtractor...\")\n", "activation_extractor = GenomeOceanActivationExtractor(genomeocean_config)\n", "print(\"Initialized GenomeOceanActivationExtractor\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Extract activations\n", "print(\"Extracting activations...\")\n", "activations = activation_extractor.extract_activations(\n", "    input_ids=tokenized[\"input_ids\"].to(config[\"device\"]),\n", "    attention_mask=tokenized[\"attention_mask\"].to(config[\"device\"]),\n", "    target_layer=config[\"target_layer\"],\n", "    batch_size=config[\"batch_size\"]\n", ")\n", "\n", "print(f\"Extracted activations with shape: {activations.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Labeled Data\n", "\n", "Finally, let's save the labeled data for later use."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Convert labels to numpy array\n", "labels_array = np.array(labels)\n", "\n", "# Save activations\n", "activations_path = os.path.join(config[\"output_dir\"], \"activations.pt\")\n", "torch.save(activations, activations_path)\n", "print(f\"Saved activations to {activations_path}\")\n", "\n", "# Save labels\n", "labels_path = os.path.join(config[\"output_dir\"], \"labels.npy\")\n", "np.save(labels_path, labels_array)\n", "print(f\"Saved labels to {labels_path}\")\n", "\n", "# Save class names\n", "class_names = np.array([feature_classes[i] for i in range(len(feature_classes))])\n", "class_names_path = os.path.join(config[\"output_dir\"], \"class_names.npy\")\n", "np.save(class_names_path, class_names)\n", "print(f\"Saved class names to {class_names_path}\")\n", "\n", "# Save raw sequences\n", "sequences_path = os.path.join(config[\"output_dir\"], \"sequences.txt\")\n", "with open(sequences_path, \"w\") as f:\n", "    for i, seq in enumerate(labeled_sequences):\n", "        f.write(f\"{i}\\t{labels[i]}\\t{seq}\\n\")\n", "print(f\"Saved raw sequences to {sequences_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to:\n", "\n", "1. Load genomic data with annotations\n", "2. Extract labeled sequences for different genomic features\n", "3. Tokenize the sequences and extract activations\n", "4. Save the labeled data for later use\n", "\n", "The labeled data can now be used for mapping genomic concepts to Sparse Autoencoder features, which we'll cover in the next notebook."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
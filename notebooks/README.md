# GOSAE Jupyter Notebooks

This directory contains Jupyter notebooks that demonstrate how to use the GenomeOcean Sparse Autoencoder (GOSAE) framework for analyzing genomic data.

## Notebooks

1. **[Prepare Genomic Data](1_prepare_genomic_data.ipynb)**: Demonstrates how to load, process, and tokenize genomic data for training a Sparse Autoencoder.

2. **[Train Sparse Autoencoder](2_train_sparse_autoencoder.ipynb)**: Shows how to extract activations from the GenomeOcean model and train a Sparse Autoencoder on them.

3. **[Prepare Labeled Genomic Data](3_prepare_labeled_genomic_data.ipynb)**: Explains how to prepare genomic data with known labels for mapping genomic concepts.

4. **[Classification and Visualization](4_classification_and_visualization.ipynb)**: Demonstrates how to classify genomic concepts using Sparse Autoencoder features and visualize the results.

## Usage

To run these notebooks, you'll need to have <PERSON><PERSON><PERSON> installed. You can install it with:

```bash
pip install jupyter
```

Then, you can start the <PERSON><PERSON>ter notebook server with:

```bash
jupyter notebook
```

This will open a browser window where you can navigate to and open the notebooks.

## Requirements

These notebooks require the GOSAE package and its dependencies to be installed. You can install them with:

```bash
pip install -e .
```

from the root directory of the repository.

## Data

The notebooks use genomic data from the `test_data/zymo` directory, which contains genome files from the Zymo mock community. You can use your own genomic data by modifying the `data_dir` parameter in the notebooks.

## Output

The notebooks save their outputs to the following directories:

- `data/processed`: Processed genomic data
- `data/labeled`: Labeled genomic data
- `models`: Trained Sparse Autoencoder models
- `results`: Analysis results and visualizations

These directories will be created automatically if they don't exist.

## Workflow

The notebooks are designed to be run in sequence, as each notebook builds on the outputs of the previous ones. However, you can also run them independently if you have the required input files.

1. Start with `1_prepare_genomic_data.ipynb` to process genomic data
2. Then run `2_train_sparse_autoencoder.ipynb` to train a Sparse Autoencoder
3. Next, run `3_prepare_labeled_genomic_data.ipynb` to prepare labeled data
4. Finally, run `4_classification_and_visualization.ipynb` to analyze and visualize the results

{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Classification and Visualization of Genomic Concepts\n", "\n", "This notebook demonstrates how to classify genomic concepts using Sparse Autoencoder features and visualize the results. We'll cover:\n", "\n", "1. Loading a trained Sparse Autoencoder model\n", "2. Loading labeled genomic data\n", "3. Extracting features from the Sparse Autoencoder\n", "4. Training a classifier on the features\n", "5. Visualizing feature importance for different genomic concepts\n", "6. Interpreting the results\n", "\n", "## Setup\n", "\n", "First, let's import the necessary libraries and set up the environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import torch\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from tqdm.notebook import tqdm\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.metrics import classification_report, confusion_matrix, accuracy_score\n", "from sklearn.manifold import TSNE\n", "from sklearn.decomposition import PCA\n", "\n", "# Import GOSAE components\n", "from gosae.model.sparse_autoencoder import SparseAutoencoder\n", "from gosae.visualization.feature_visualizer import FeatureVisualizer\n", "\n", "# Set random seed for reproducibility\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration\n", "\n", "Let's set up the configuration for our analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "config = {\n", "    \"model_dir\": \"../models\",  # Directory containing the trained model\n", "    \"data_dir\": \"../data/labeled\",  # Directory containing labeled data\n", "    \"output_dir\": \"../results\",  # Directory to save results\n", "    \"device\": \"cuda\" if torch.cuda.is_available() else \"cpu\",  # Device to use\n", "    \"test_size\": 0.2,  # Test set size for classification\n", "    \"n_estimators\": 100,  # Number of trees in the random forest\n", "    \"random_state\": 42  # Random seed\n", "}\n", "\n", "# Create output directory if it doesn't exist\n", "os.makedirs(config[\"output_dir\"], exist_ok=True)\n", "\n", "print(f\"Using device: {config['device']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Model and Data\n", "\n", "Let's load the trained Sparse Autoencoder model and the labeled data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load model\n", "model_path = os.path.join(config[\"model_dir\"], \"sae_model.pt\")\n", "print(f\"Loading model from {model_path}...\")\n", "model = SparseAutoencoder.load(model_path, device=config[\"device\"])\n", "print(\"Model loaded successfully\")\n", "print(f\"  Input dimension: {model.config.input_dim}\")\n", "print(f\"  Hidden dimension: {model.config.hidden_dim}\")\n", "print(f\"  Latent dimension: {model.config.latent_dim}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load activations and labels\n", "activations_path = os.path.join(config[\"data_dir\"], \"activations.pt\")\n", "labels_path = os.path.join(config[\"data_dir\"], \"labels.npy\")\n", "class_names_path = os.path.join(config[\"data_dir\"], \"class_names.npy\")\n", "\n", "print(f\"Loading data from {config['data_dir']}...\")\n", "activations = torch.load(activations_path, map_location=config[\"device\"])\n", "labels = np.load(labels_path)\n", "class_names = np.load(class_names_path)\n", "\n", "print(\"Data loaded successfully\")\n", "print(f\"  Activations shape: {activations.shape}\")\n", "print(f\"  Labels shape: {labels.shape}\")\n", "print(f\"  Number of classes: {len(class_names)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Features\n", "\n", "Now, let's extract features from the Sparse Autoencoder using the activations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Extract features\n", "print(\"Extracting features...\")\n", "with torch.no_grad():\n", "    _, features = model(activations)\n", "\n", "# Convert to numpy for scikit-learn\n", "features_np = features.cpu().numpy()\n", "print(f\"Extracted features with shape: {features_np.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Feature Sparsity\n", "\n", "Let's analyze the sparsity of the extracted features."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Compute feature sparsity\n", "feature_sparsity = (features == 0).float().mean(dim=0).cpu().numpy()\n", "feature_means = features.mean(dim=0).cpu().numpy()\n", "feature_stds = features.std(dim=0).cpu().numpy()\n", "\n", "# Plot feature sparsity\n", "plt.figure(figsize=(12, 6))\n", "plt.subplot(1, 3, 1)\n", "plt.hist(feature_sparsity, bins=50, alpha=0.7)\n", "plt.title(\"Feature Sparsity\")\n", "plt.xlabel(\"Sparsity (fraction of zeros)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.hist(feature_means, bins=50, alpha=0.7)\n", "plt.title(\"Feature Means\")\n", "plt.xlabel(\"Mean Activation\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.hist(feature_stds, bins=50, alpha=0.7)\n", "plt.title(\"Feature Standard Deviations\")\n", "plt.xlabel(\"Standard Deviation\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Classifier\n", "\n", "Now, let's train a classifier on the extracted features to predict genomic concepts."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Split data into train and test sets\n", "X_train, X_test, y_train, y_test = train_test_split(\n", "    features_np, labels, test_size=config[\"test_size\"], random_state=config[\"random_state\"], stratify=labels\n", ")\n", "\n", "print(f\"Training set: {X_train.shape[0]} samples\")\n", "print(f\"Test set: {X_test.shape[0]} samples\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Train a random forest classifier\n", "print(\"Training random forest classifier...\")\n", "clf = RandomForestClassifier(\n", "    n_estimators=config[\"n_estimators\"],\n", "    random_state=config[\"random_state\"],\n", "    n_jobs=-1\n", ")\n", "clf.fit(X_train, y_train)\n", "print(\"Classifier trained successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Evaluate the classifier\n", "y_pred = clf.predict(X_test)\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f\"Accuracy: {accuracy:.4f}\")\n", "\n", "# Print classification report\n", "print(\"\\nClassification Report:\")\n", "print(classification_report(y_test, y_pred, target_names=class_names))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot confusion matrix\n", "plt.figure(figsize=(10, 8))\n", "cm = confusion_matrix(y_test, y_pred)\n", "cm_normalized = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]\n", "sns.heatmap(cm_normalized, annot=True, fmt=\".2f\", cmap=\"Blues\", xticklabels=class_names, yticklabels=class_names)\n", "plt.title(\"Normalized Confusion Matrix\")\n", "plt.xlabel(\"Predicted Label\")\n", "plt.ylabel(\"True Label\")\n", "plt.xticks(rotation=45, ha=\"right\")\n", "plt.yticks(rotation=0)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Feature Importance\n", "\n", "Let's analyze the importance of different features for classifying genomic concepts."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Get feature importances\n", "feature_importances = clf.feature_importances_\n", "\n", "# Plot top 20 most important features\n", "plt.figure(figsize=(12, 6))\n", "top_indices = np.argsort(feature_importances)[-20:]\n", "plt.barh(range(20), feature_importances[top_indices])\n", "plt.yticks(range(20), [f\"Feature {i}\" for i in top_indices])\n", "plt.title(\"Top 20 Most Important Features\")\n", "plt.xlabel(\"Feature Importance\")\n", "plt.ylabel(\"Feature\")\n", "plt.grid(axis=\"x\", alpha=0.3)\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Feature Space\n", "\n", "Let's visualize the feature space using dimensionality reduction techniques."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Apply PCA for dimensionality reduction\n", "print(\"Applying PCA...\")\n", "pca = PCA(n_components=50)\n", "features_pca = pca.fit_transform(features_np)\n", "print(f\"PCA explained variance ratio: {np.sum(pca.explained_variance_ratio_):.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Apply t-SNE for visualization\n", "print(\"Applying t-SNE...\")\n", "tsne = TSNE(n_components=2, random_state=config[\"random_state\"], perplexity=30, n_iter=1000)\n", "features_tsne = tsne.fit_transform(features_pca)\n", "print(\"t-SNE completed successfully\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot t-SNE visualization\n", "plt.figure(figsize=(12, 10))\n", "scatter = plt.scatter(features_tsne[:, 0], features_tsne[:, 1], c=labels, cmap=\"tab10\", alpha=0.7, s=10)\n", "plt.colorbar(scatter, ticks=range(len(class_names)), label=\"Class\")\n", "plt.title(\"t-SNE Visualization of Feature Space\")\n", "plt.xlabel(\"t-SNE Dimension 1\")\n", "plt.ylabel(\"t-SNE Dimension 2\")\n", "plt.grid(alpha=0.3)\n", "\n", "# Add legend\n", "legend_elements = [plt.Line2D([0], [0], marker=\"o\", color=\"w\", markerfacecolor=scatter.cmap(scatter.norm(i)), \n", "                             markersize=10, label=class_names[i]) for i in range(len(class_names))]\n", "plt.legend(handles=legend_elements, title=\"Classes\", loc=\"best\")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Feature Activations by Class\n", "\n", "Let's visualize how different features activate for different genomic concepts."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Compute mean feature activations by class\n", "class_feature_means = []\n", "for class_id in range(len(class_names)):\n", "    class_mask = (labels == class_id)\n", "    if np.sum(class_mask) > 0:\n", "        class_features = features_np[class_mask]\n", "        class_mean = np.mean(class_features, axis=0)\n", "        class_feature_means.append(class_mean)\n", "    else:\n", "        class_feature_means.append(np.zeros(features_np.shape[1]))\n", "\n", "class_feature_means = np.array(class_feature_means)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Find top features for each class\n", "top_k = 10\n", "top_features_by_class = {}\n", "\n", "for class_id in range(len(class_names)):\n", "    class_mean = class_feature_means[class_id]\n", "    top_indices = np.argsort(class_mean)[-top_k:]\n", "    top_values = class_mean[top_indices]\n", "    top_features_by_class[class_id] = (top_indices, top_values)\n", "\n", "# Print top features for each class\n", "print(\"Top Features by Class:\")\n", "for class_id in range(len(class_names)):\n", "    print(f\"\\n{class_names[class_id]}:\")\n", "    top_indices, top_values = top_features_by_class[class_id]\n", "    for i, (idx, val) in enumerate(zip(top_indices[::-1], top_values[::-1])):\n", "        print(f\"  {i+1}. Feature {idx}: {val:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot heatmap of top features by class\n", "plt.figure(figsize=(14, 10))\n", "\n", "# Get top 20 features across all classes\n", "all_top_indices = set()\n", "for class_id in range(len(class_names)):\n", "    top_indices, _ = top_features_by_class[class_id]\n", "    all_top_indices.update(top_indices)\n", "\n", "all_top_indices = sorted(list(all_top_indices))[:50]  # Limit to 50 features\n", "\n", "# Create heatmap data\n", "heatmap_data = class_feature_means[:, all_top_indices]\n", "\n", "# Plot heatmap\n", "sns.heatmap(heatmap_data, annot=False, cmap=\"viridis\", xticklabels=[f\"F{i}\" for i in all_top_indices], yticklabels=class_names)\n", "plt.title(\"Feature Activations by Class\")\n", "plt.xlabel(\"Feature\")\n", "plt.ylabel(\"Class\")\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Visualize Feature Decoder Weights\n", "\n", "Let's visualize the decoder weights for the top features to understand what patterns they're detecting."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize feature visualizer\n", "visualizer = FeatureVisualizer(model)\n", "\n", "# Get decoder weights\n", "decoder_weights = model.decoder.weight.cpu().numpy()\n", "\n", "# Plot decoder weights for top features\n", "num_features_to_plot = 5\n", "plt.figure(figsize=(15, 10))\n", "\n", "for class_id in range(min(4, len(class_names))):\n", "    top_indices, _ = top_features_by_class[class_id]\n", "    for i, idx in enumerate(top_indices[-num_features_to_plot:]):\n", "        plt.subplot(4, num_features_to_plot, class_id * num_features_to_plot + i + 1)\n", "        plt.plot(decoder_weights[idx])\n", "        plt.title(f\"{class_names[class_id]}\\nFeature {idx}\")\n", "        plt.grid(alpha=0.3)\n", "        if i == 0:\n", "            plt.ylabel(\"Weight\")\n", "        if class_id == 3:\n", "            plt.xlabel(\"Dimension\")\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Results\n", "\n", "Finally, let's save the results for later use."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Save feature importances\n", "importances_df = pd.DataFrame({\n", "    \"Feature\": range(len(feature_importances)),\n", "    \"Importance\": feature_importances\n", "})\n", "importances_df = importances_df.sort_values(\"Importance\", ascending=False)\n", "importances_path = os.path.join(config[\"output_dir\"], \"feature_importances.csv\")\n", "importances_df.to_csv(importances_path, index=False)\n", "print(f\"Saved feature importances to {importances_path}\")\n", "\n", "# Save class feature means\n", "class_means_df = pd.DataFrame(class_feature_means, index=class_names)\n", "class_means_path = os.path.join(config[\"output_dir\"], \"class_feature_means.csv\")\n", "class_means_df.to_csv(class_means_path)\n", "print(f\"Saved class feature means to {class_means_path}\")\n", "\n", "# Save t-SNE coordinates\n", "tsne_df = pd.DataFrame({\n", "    \"tsne_1\": features_tsne[:, 0],\n", "    \"tsne_2\": features_tsne[:, 1],\n", "    \"label\": labels\n", "})\n", "tsne_path = os.path.join(config[\"output_dir\"], \"tsne_coordinates.csv\")\n", "tsne_df.to_csv(tsne_path, index=False)\n", "print(f\"Saved t-SNE coordinates to {tsne_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to:\n", "\n", "1. <PERSON>ad a trained Sparse Autoencoder model and labeled genomic data\n", "2. Extract features from the Sparse Autoencoder\n", "3. Train a classifier to predict genomic concepts from the features\n", "4. Visualize the feature space and feature importance\n", "5. Analyze how different features activate for different genomic concepts\n", "\n", "These techniques can help us understand what genomic patterns the Sparse Autoencoder has learned to detect and how they relate to known biological concepts. By mapping features to genomic concepts, we can gain insights into the internal representations of the GenomeOcean model and potentially discover new biological patterns."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
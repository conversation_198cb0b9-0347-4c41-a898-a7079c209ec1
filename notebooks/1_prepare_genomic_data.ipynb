{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Preparing Genomic Data for Sparse Autoencoder Training\n", "\n", "This notebook demonstrates how to prepare genomic data for training a Sparse Autoencoder (SAE) on GenomeOcean model activations. We'll cover:\n", "\n", "1. Loading genomic data from FASTA files\n", "2. Processing and tokenizing the sequences\n", "3. Saving the processed data for later use\n", "\n", "## Setup\n", "\n", "First, let's import the necessary libraries and set up the environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import torch\n", "import numpy as np\n", "from pathlib import Path\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# Import GOSAE components\n", "from gosae.data.genomic_data import GenomicDataProcessor\n", "from gosae.data.dna_tokenizer import DNATokenizer\n", "from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_token_limit\n", "\n", "# Set random seed for reproducibility\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration\n", "\n", "Let's set up the configuration for our data processing."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "config = {\n", "    \"data_dir\": \"../test_data/zymo\",  # Directory containing genomic data\n", "    \"output_dir\": \"../data/processed\",  # Directory to save processed data\n", "    \"model_name\": DEFAULT_MODEL,  # GenomeOcean model name\n", "    \"max_length\": get_model_token_limit(DEFAULT_MODEL),  # Maximum token length\n", "    \"overlap\": 128,  # Overlap between sequences\n", "    \"batch_size\": 8,  # Batch size for processing\n", "    \"device\": \"cuda\" if torch.cuda.is_available() else \"cpu\"  # Device to use\n", "}\n", "\n", "# Create output directory if it doesn't exist\n", "os.makedirs(config[\"output_dir\"], exist_ok=True)\n", "\n", "print(f\"Using device: {config['device']}\")\n", "print(f\"Model: {config['model_name']}\")\n", "print(f\"Max token length: {config['max_length']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Data Processor\n", "\n", "Now, let's initialize the GenomicDataProcessor, which will help us load and process genomic data."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize data processor\n", "data_processor = GenomicDataProcessor(\n", "    max_length=config[\"max_length\"],\n", "    overlap=config[\"overlap\"],\n", "    random_crop=False,\n", "    tokenizer_model=config[\"model_name\"]\n", ")\n", "\n", "print(f\"Initialized GenomicDataProcessor with max_length={config['max_length']} and overlap={config['overlap']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load and Process Genomic Data\n", "\n", "Let's load the genomic data from FASTA files and process it."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Get list of FASTA files\n", "fasta_files = [f for f in os.listdir(config[\"data_dir\"]) if f.endswith(\".fasta\")]\n", "print(f\"Found {len(fasta_files)} FASTA files in {config['data_dir']}:\")\n", "for i, file in enumerate(fasta_files):\n", "    print(f\"  {i+1}. {file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Select a subset of files for processing\n", "selected_files = fasta_files[:3]  # Select the first 3 files\n", "print(f\"Selected {len(selected_files)} files for processing:\")\n", "for i, file in enumerate(selected_files):\n", "    print(f\"  {i+1}. {file}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Process each file\n", "all_sequences = []\n", "all_labels = []\n", "\n", "for file_idx, file in enumerate(selected_files):\n", "    file_path = os.path.join(config[\"data_dir\"], file)\n", "    print(f\"\\nProcessing file {file_idx+1}/{len(selected_files)}: {file}\")\n", "    \n", "    # Load sequences from FASTA file\n", "    seq_records = data_processor.load_fasta(file_path)\n", "    print(f\"Loaded {len(seq_records)} sequences from {file}\")\n", "    \n", "    # Process sequences\n", "    processed_sequences = data_processor.process_sequences(seq_records)\n", "    print(f\"Processed into {len(processed_sequences)} sequences\")\n", "    \n", "    # Add to list\n", "    all_sequences.extend(processed_sequences)\n", "    \n", "    # Create labels (file index)\n", "    all_labels.extend([file_idx] * len(processed_sequences))\n", "\n", "print(f\"\\nTotal processed sequences: {len(all_sequences)}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Sequence Statistics\n", "\n", "Let's analyze the statistics of the processed sequences."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Get sequence statistics\n", "stats = data_processor.get_sequence_stats(all_sequences)\n", "\n", "# Print statistics\n", "print(\"Sequence Statistics:\")\n", "print(f\"  Number of sequences: {stats['num_sequences']}\")\n", "print(f\"  Total length: {stats['total_length']} bp\")\n", "print(f\"  Min length: {stats['min_length']} bp\")\n", "print(f\"  Max length: {stats['max_length']} bp\")\n", "print(f\"  Mean length: {stats['mean_length']:.2f} bp\")\n", "print(f\"  Median length: {stats['median_length']} bp\")\n", "\n", "# Print nucleotide frequencies\n", "print(\"\\nNucleotide Frequencies:\")\n", "for nucleotide, freq in stats[\"nucleotide_frequencies\"].items():\n", "    print(f\"  {nucleotide}: {freq:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot nucleotide frequencies\n", "plt.figure(figsize=(10, 6))\n", "nucleotides = list(stats[\"nucleotide_frequencies\"].keys())\n", "frequencies = list(stats[\"nucleotide_frequencies\"].values())\n", "plt.bar(nucleotides, frequencies)\n", "plt.title(\"Nucleotide Frequencies\")\n", "plt.xlabel(\"Nucleotide\")\n", "plt.ylabel(\"Frequency\")\n", "plt.ylim(0, 0.5)\n", "plt.grid(axis=\"y\", alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Tokenize Sequences\n", "\n", "Now, let's tokenize the sequences using the DNATokenizer."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Tokenize sequences\n", "print(\"Tokenizing sequences...\")\n", "tokenized = data_processor.tokenize_sequences(\n", "    sequences=all_sequences,\n", "    batch_size=config[\"batch_size\"],\n", "    return_tensors=\"pt\",\n", "    padding=True,\n", "    truncation=True\n", ")\n", "\n", "print(f\"Tokenized {len(all_sequences)} sequences\")\n", "print(f\"Input IDs shape: {tokenized['input_ids'].shape}\")\n", "print(f\"Attention mask shape: {tokenized['attention_mask'].shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Processed Data\n", "\n", "Finally, let's save the processed data for later use."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Save tokenized sequences\n", "tokenized_path = os.path.join(config[\"output_dir\"], \"tokenized_sequences.pt\")\n", "torch.save(tokenized, tokenized_path)\n", "print(f\"Saved tokenized sequences to {tokenized_path}\")\n", "\n", "# Save raw sequences\n", "sequences_path = os.path.join(config[\"output_dir\"], \"raw_sequences.pt\")\n", "torch.save(all_sequences, sequences_path)\n", "print(f\"Saved raw sequences to {sequences_path}\")\n", "\n", "# Save labels\n", "labels_path = os.path.join(config[\"output_dir\"], \"labels.npy\")\n", "np.save(labels_path, np.array(all_labels))\n", "print(f\"Saved labels to {labels_path}\")\n", "\n", "# Save file names for reference\n", "file_names_path = os.path.join(config[\"output_dir\"], \"file_names.txt\")\n", "with open(file_names_path, \"w\") as f:\n", "    for i, file in enumerate(selected_files):\n", "        f.write(f\"{i}: {file}\\n\")\n", "print(f\"Saved file names to {file_names_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to:\n", "\n", "1. Load genomic data from FASTA files\n", "2. Process and tokenize the sequences\n", "3. Analyze sequence statistics\n", "4. Save the processed data for later use\n", "\n", "The processed data can now be used for extracting activations from the GenomeOcean model and training a Sparse Autoencoder, which we'll cover in the next notebook."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
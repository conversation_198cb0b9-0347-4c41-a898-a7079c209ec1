{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Training a Sparse Autoencoder on GenomeOcean Model Activations\n", "\n", "This notebook demonstrates how to train a Sparse Autoencoder (SAE) on activations extracted from the GenomeOcean model. We'll cover:\n", "\n", "1. Loading processed genomic data\n", "2. Extracting activations from the GenomeOcean model\n", "3. Training a Sparse Autoencoder on the extracted activations\n", "4. Evaluating the trained model\n", "5. Saving the model for later use\n", "\n", "## Setup\n", "\n", "First, let's import the necessary libraries and set up the environment."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["import os\n", "import torch\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "from tqdm.notebook import tqdm\n", "\n", "# Import GOSAE components\n", "from gosae.model.sparse_autoencoder import SparseAutoencoder\n", "from gosae.model.activation_extractor import GenomeOceanActivationExtractor\n", "from gosae.data.genomic_data import GenomicDataProcessor\n", "from gosae.utils.config import GenomeOceanConfig, SparseAutoencoderConfig, TrainingConfig\n", "from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_embedding_size\n", "\n", "# Set random seed for reproducibility\n", "torch.manual_seed(42)\n", "np.random.seed(42)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Configuration\n", "\n", "Let's set up the configuration for our model and training."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Configuration\n", "config = {\n", "    \"data_dir\": \"../data/processed\",  # Directory containing processed data\n", "    \"output_dir\": \"../models\",  # Directory to save models\n", "    \"model_name\": DEFAULT_MODEL,  # GenomeOcean model name\n", "    \"target_layer\": \"model.layers.8.mlp.up_proj\",  # Target layer for activation extraction\n", "    \"batch_size\": 8,  # Batch size for processing\n", "    \"device\": \"cuda\" if torch.cuda.is_available() else \"cpu\",  # Device to use\n", "    \"num_epochs\": 10,  # Number of epochs for training\n", "    \"learning_rate\": 3e-4,  # Learning rate\n", "    \"l1_coefficient\": 1e-3,  # L1 regularization coefficient\n", "    \"topk\": 32,  # BatchTopK parameter (0 to disable)\n", "    \"seed\": 42  # Random seed\n", "}\n", "\n", "# Create output directory if it doesn't exist\n", "os.makedirs(config[\"output_dir\"], exist_ok=True)\n", "\n", "print(f\"Using device: {config['device']}\")\n", "print(f\"Model: {config['model_name']}\")\n", "print(f\"Target layer: {config['target_layer']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Load Processed Data\n", "\n", "Let's load the processed genomic data that we prepared in the previous notebook."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Load tokenized sequences\n", "tokenized_path = os.path.join(config[\"data_dir\"], \"tokenized_sequences.pt\")\n", "tokenized = torch.load(tokenized_path)\n", "print(f\"Loaded tokenized sequences from {tokenized_path}\")\n", "print(f\"Input IDs shape: {tokenized['input_ids'].shape}\")\n", "print(f\"Attention mask shape: {tokenized['attention_mask'].shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Extract Activations\n", "\n", "Now, let's extract activations from the GenomeOcean model using the tokenized sequences."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize GenomeOcean activation extractor\n", "genomeocean_config = GenomeOceanConfig(\n", "    model_name=config[\"model_name\"],\n", "    target_layers=[config[\"target_layer\"]],\n", "    cache_dir=os.path.join(config[\"output_dir\"], \"cache\"),\n", "    use_cache=True\n", ")\n", "\n", "print(\"Initializing GenomeOceanActivationExtractor...\")\n", "activation_extractor = GenomeOceanActivationExtractor(genomeocean_config)\n", "print(\"Initialized GenomeOceanActivationExtractor\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Extract activations\n", "print(\"Extracting activations...\")\n", "activations = activation_extractor.extract_activations(\n", "    input_ids=tokenized[\"input_ids\"].to(config[\"device\"]),\n", "    attention_mask=tokenized[\"attention_mask\"].to(config[\"device\"]),\n", "    target_layer=config[\"target_layer\"],\n", "    batch_size=config[\"batch_size\"]\n", ")\n", "\n", "print(f\"Extracted activations with shape: {activations.shape}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Activations\n", "\n", "Let's analyze the extracted activations to understand their properties."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Compute activation statistics\n", "activation_mean = activations.mean().item()\n", "activation_std = activations.std().item()\n", "activation_min = activations.min().item()\n", "activation_max = activations.max().item()\n", "activation_sparsity = (activations == 0).float().mean().item()\n", "\n", "print(\"Activation Statistics:\")\n", "print(f\"  Mean: {activation_mean:.4f}\")\n", "print(f\"  Std: {activation_std:.4f}\")\n", "print(f\"  Min: {activation_min:.4f}\")\n", "print(f\"  Max: {activation_max:.4f}\")\n", "print(f\"  Sparsity: {activation_sparsity:.4f}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot activation distribution\n", "plt.figure(figsize=(10, 6))\n", "plt.hist(activations.flatten().cpu().numpy(), bins=100, alpha=0.7)\n", "plt.title(\"Activation Distribution\")\n", "plt.xlabel(\"Activation Value\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize Sparse Autoencoder\n", "\n", "Now, let's initialize the Sparse Autoencoder model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Get input dimension from activations\n", "input_dim = activations.shape[-1]\n", "\n", "# Set latent dimension (typically 2x input dimension)\n", "latent_dim = 2 * input_dim\n", "\n", "# Initialize Sparse Autoencoder\n", "sae_config = SparseAutoencoderConfig(\n", "    input_dim=input_dim,\n", "    latent_dim=latent_dim,\n", "    hidden_dim=input_dim,  # Same as input dimension\n", "    sparsity_target=0.05,  # Target activation rate\n", "    l1_coefficient=config[\"l1_coefficient\"],\n", "    topk=config[\"topk\"],\n", "    device=config[\"device\"]\n", ")\n", "\n", "print(\"Initializing SparseAutoencoder...\")\n", "model = SparseAutoencoder(sae_config)\n", "model.to(config[\"device\"])\n", "print(\"Initialized SparseAutoencoder\")\n", "print(f\"  Input dimension: {input_dim}\")\n", "print(f\"  Hidden dimension: {input_dim}\")\n", "print(f\"  Latent dimension: {latent_dim}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Train Sparse Autoencoder\n", "\n", "Now, let's train the Sparse Autoencoder on the extracted activations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Initialize optimizer\n", "optimizer = torch.optim.Adam(\n", "    model.parameters(),\n", "    lr=config[\"learning_rate\"],\n", "    betas=(0.9, 0.999),\n", "    weight_decay=0.0\n", ")\n", "\n", "# Training loop\n", "print(\"Starting training...\")\n", "num_batches = (activations.shape[0] + config[\"batch_size\"] - 1) // config[\"batch_size\"]\n", "train_losses = []\n", "recon_losses = []\n", "l1_losses = []\n", "\n", "for epoch in range(config[\"num_epochs\"]):\n", "    epoch_loss = 0.0\n", "    epoch_recon_loss = 0.0\n", "    epoch_l1_loss = 0.0\n", "    \n", "    # Shuffle data\n", "    indices = torch.randperm(activations.shape[0])\n", "    shuffled_activations = activations[indices]\n", "    \n", "    # Process batches\n", "    for i in range(0, activations.shape[0], config[\"batch_size\"]):\n", "        # Get batch\n", "        batch = shuffled_activations[i:i+config[\"batch_size\"]].to(config[\"device\"])\n", "        \n", "        # Forward pass\n", "        optimizer.zero_grad()\n", "        reconstructed, latent = model(batch)\n", "        \n", "        # Compute loss\n", "        loss_dict = model.loss(batch, reconstructed, latent)\n", "        loss = loss_dict[\"total_loss\"]\n", "        \n", "        # Backward pass\n", "        loss.backward()\n", "        optimizer.step()\n", "        \n", "        # Update metrics\n", "        epoch_loss += loss.item() * batch.shape[0]\n", "        epoch_recon_loss += loss_dict[\"recon_loss\"].item() * batch.shape[0]\n", "        epoch_l1_loss += loss_dict[\"l1_loss\"].item() * batch.shape[0]\n", "    \n", "    # Compute epoch metrics\n", "    epoch_loss /= activations.shape[0]\n", "    epoch_recon_loss /= activations.shape[0]\n", "    epoch_l1_loss /= activations.shape[0]\n", "    \n", "    # Save metrics\n", "    train_losses.append(epoch_loss)\n", "    recon_losses.append(epoch_recon_loss)\n", "    l1_losses.append(epoch_l1_loss)\n", "    \n", "    # Print progress\n", "    print(f\"Epoch {epoch+1}/{config['num_epochs']}: \"\n", "          f\"Loss = {epoch_loss:.4f}, \"\n", "          f\"Recon Loss = {epoch_recon_loss:.4f}, \"\n", "          f\"L1 Loss = {epoch_l1_loss:.4f}\")\n", "\n", "print(\"Training completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Evaluate Training\n", "\n", "Let's evaluate the training by plotting the loss curves."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Plot loss curves\n", "plt.figure(figsize=(12, 6))\n", "\n", "plt.subplot(1, 3, 1)\n", "plt.plot(train_losses)\n", "plt.title(\"Total Loss\")\n", "plt.xlabel(\"Epoch\")\n", "plt.ylabel(\"Loss\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.plot(recon_losses)\n", "plt.title(\"Reconstruction Loss\")\n", "plt.xlabel(\"Epoch\")\n", "plt.ylabel(\"Loss\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.plot(l1_losses)\n", "plt.title(\"L1 Loss\")\n", "plt.xlabel(\"Epoch\")\n", "plt.ylabel(\"Loss\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Analyze Feature Activations\n", "\n", "Let's analyze the feature activations of the trained model."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Get feature activations\n", "with torch.no_grad():\n", "    _, latent = model(activations.to(config[\"device\"]))\n", "\n", "# Compute feature statistics\n", "feature_means = latent.mean(dim=0).cpu().numpy()\n", "feature_stds = latent.std(dim=0).cpu().numpy()\n", "feature_sparsity = (latent == 0).float().mean(dim=0).cpu().numpy()\n", "\n", "# Plot feature activation statistics\n", "plt.figure(figsize=(15, 5))\n", "\n", "plt.subplot(1, 3, 1)\n", "plt.hist(feature_means, bins=50, alpha=0.7)\n", "plt.title(\"Feature Mean Activations\")\n", "plt.xlabel(\"Mean Activation\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.subplot(1, 3, 2)\n", "plt.hist(feature_stds, bins=50, alpha=0.7)\n", "plt.title(\"Feature Standard Deviations\")\n", "plt.xlabel(\"Standard Deviation\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.subplot(1, 3, 3)\n", "plt.hist(feature_sparsity, bins=50, alpha=0.7)\n", "plt.title(\"Feature Sparsity\")\n", "plt.xlabel(\"Sparsity (fraction of zeros)\")\n", "plt.ylabel(\"Frequency\")\n", "plt.grid(alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Save Model and Activations\n", "\n", "Finally, let's save the trained model and activations for later use."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "source": ["# Save model\n", "model_path = os.path.join(config[\"output_dir\"], \"sae_model.pt\")\n", "model.save(model_path)\n", "print(f\"Saved model to {model_path}\")\n", "\n", "# Save activations\n", "activations_path = os.path.join(config[\"output_dir\"], \"activations.pt\")\n", "torch.save(activations, activations_path)\n", "print(f\"Saved activations to {activations_path}\")\n", "\n", "# Save feature activations\n", "latent_path = os.path.join(config[\"output_dir\"], \"feature_activations.pt\")\n", "torch.save(latent, latent_path)\n", "print(f\"Saved feature activations to {latent_path}\")\n", "\n", "# Save configuration\n", "config_path = os.path.join(config[\"output_dir\"], \"config.json\")\n", "sae_config.save(config_path)\n", "print(f\"Saved configuration to {config_path}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Conclusion\n", "\n", "In this notebook, we've demonstrated how to:\n", "\n", "1. Load processed genomic data\n", "2. Extract activations from the GenomeOcean model\n", "3. Train a Sparse Autoencoder on the extracted activations\n", "4. <PERSON><PERSON>ate the trained model\n", "5. Save the model for later use\n", "\n", "The trained model can now be used for analyzing genomic concepts and features, which we'll cover in the next notebooks."]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.0"}}, "nbformat": 4, "nbformat_minor": 4}
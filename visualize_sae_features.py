#!/usr/bin/env python
"""
Wrapper script for visualizing features discovered by a sparse autoencoder.
This script uses the FeatureVisualizer class to generate visualizations.
"""

import os
import torch
import argparse
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Optional

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.visualization.feature_visualizer import FeatureVisualizer


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Visualize features discovered by a sparse autoencoder.")
    
    # Existing arguments
    parser.add_argument("--model_path", type=str, required=True, help="Path to the trained sparse autoencoder model.")
    parser.add_argument("--activations_path", type=str, required=True, help="Path to the saved activations.")
    parser.add_argument("--output_dir", type=str, default="visualizations", help="Output directory for visualizations.")
    parser.add_argument("--top_n", type=int, default=20, help="Number of top features to visualize.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use.")
    parser.add_argument("--visualize_all", action="store_true", help="Generate all visualizations.")
    parser.add_argument("--visualize_importance", action="store_true", help="Generate feature importance visualization.")
    parser.add_argument("--visualize_correlation", action="store_true", help="Generate feature correlation visualization.")
    parser.add_argument("--visualize_embedding", action="store_true", help="Generate feature embedding visualization.")
    parser.add_argument("--visualize_activations", action="store_true", help="Generate individual feature activations.")
    parser.add_argument("--visualize_heatmap", action="store_true", help="Generate feature heatmap visualization.")
    parser.add_argument("--embedding_method", type=str, default="tsne", choices=["pca", "tsne"], help="Method for embedding visualization.")
    
    # Add batch size argument
    parser.add_argument("--batch_size", type=int, default=1000, help="Batch size for processing activations to avoid OOM errors.")
    
    return parser.parse_args()


def visualize_features(
    model_path: str,
    activations_path: str,
    output_dir: str = "visualizations",
    top_n: int = 20,
    device: str = "cuda" if torch.cuda.is_available() else "cpu",
    visualize_all: bool = False,
    visualize_importance: bool = False,
    visualize_correlation: bool = False,
    visualize_embedding: bool = False,
    visualize_activations: bool = False,
    visualize_heatmap: bool = False,
    embedding_method: str = "tsne",
    batch_size: int = 1000,  # Add batch size parameter
):
    """
    Visualize features discovered by a sparse autoencoder.
    
    Args:
        model_path: Path to the trained sparse autoencoder model.
        activations_path: Path to the saved activations.
        output_dir: Output directory for visualizations.
        top_n: Number of top features to visualize.
        device: Device to use.
        visualize_all: Generate all visualizations.
        visualize_importance: Generate feature importance visualization.
        visualize_correlation: Generate feature correlation visualization.
        visualize_embedding: Generate feature embedding visualization.
        visualize_activations: Generate individual feature activations.
        visualize_heatmap: Generate feature heatmap visualization.
        embedding_method: Method for embedding visualization.
        batch_size: Number of activations to process at once to avoid OOM errors.
    """
    # Create output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Load the model
    print(f"Loading model from {model_path}...")
    model = SparseAutoencoder.load(model_path, device=device)
    
    # Load activations
    print(f"Loading activations from {activations_path}...")
    activations = torch.load(activations_path, map_location="cpu")  # Load to CPU first
    
    # Create visualizer
    visualizer = FeatureVisualizer(output_dir=output_dir)
    
    # Process activations in batches to avoid OOM
    print("Computing feature activations in batches...")
    total_samples = activations.shape[0]
    feature_activations_list = []
    
    for i in range(0, total_samples, batch_size):
        end_idx = min(i + batch_size, total_samples)
        print(f"Processing batch {i//batch_size + 1}/{(total_samples + batch_size - 1)//batch_size}: samples {i} to {end_idx}")
        
        batch = activations[i:end_idx].to(device)
        with torch.no_grad():
            _, batch_feature_activations = model(batch)
            # Move results back to CPU to free GPU memory
            feature_activations_list.append(batch_feature_activations.cpu())
        
        # Clear GPU cache
        torch.cuda.empty_cache()
    
    # Combine batches
    feature_activations = torch.cat(feature_activations_list, dim=0)
    
    # Compute feature importance (L1 norm of decoder weights)
    feature_importance = torch.norm(model.decoder.weight, dim=0).detach().cpu().numpy()
    feature_importance_dict = {i: float(importance) for i, importance in enumerate(feature_importance)}
    
    # Get top feature indices
    top_indices = sorted(feature_importance_dict.keys(), 
                         key=lambda k: feature_importance_dict[k], 
                         reverse=True)[:top_n]
    
    # Ensure feature indices are within bounds
    num_features = feature_activations.shape[1]
    print(f"Total number of features: {num_features}")
    print(f"Model decoder weight shape: {model.decoder.weight.shape}")
    
    # If all indices are out of bounds, use the first n features from activations
    valid_top_indices = [idx for idx in top_indices if idx < num_features]
    if len(valid_top_indices) == 0:
        print(f"Warning: All feature indices were out of bounds. Using first {top_n} features from activations.")
        valid_top_indices = list(range(min(top_n, num_features)))
    elif len(valid_top_indices) < len(top_indices):
        print(f"Warning: Some feature indices were out of bounds. Using {len(valid_top_indices)} valid indices.")
    
    top_indices = valid_top_indices
    
    # Ensure activations are 2D (samples x features)
    if feature_activations.dim() > 2:
        print(f"Reshaping activations from {feature_activations.shape} to 2D...")
        # Reshape to (samples, features) by flattening all dimensions after the first
        feature_activations = feature_activations.reshape(feature_activations.shape[0], -1)
        print(f"New activations shape: {feature_activations.shape}")
    
    # Generate visualizations based on flags
    if visualize_all or visualize_importance:
        print("Visualizing feature importance...")
        visualizer.plot_feature_importance(
            feature_importances=feature_importance_dict,
            top_n=top_n,
            title=f"Top {top_n} Feature Importances",
            save_path=os.path.join(output_dir, "feature_importance.png"),
        )
    
    if visualize_all or visualize_correlation:
        print("Visualizing feature correlations...")
        visualizer.plot_feature_correlation(
            activations=feature_activations,
            feature_indices=top_indices,
            title=f"Top {top_n} Feature Correlations",
            save_path=os.path.join(output_dir, "feature_correlation.png"),
        )
    
    if visualize_all or visualize_embedding:
        print(f"Visualizing feature embedding using {embedding_method}...")
        visualizer.plot_feature_embedding(
            decoder_weights=model.decoder.weight,
            method=embedding_method,
            feature_indices=top_indices,
            title=f"Top {top_n} Feature Embedding ({embedding_method.upper()})",
            save_path=os.path.join(output_dir, f"feature_embedding_{embedding_method}.png"),
        )
    
    if visualize_all or visualize_activations:
        print("Visualizing individual feature activations...")
        for i, feature_idx in enumerate(top_indices[:5]):  # Visualize top 5 features
            visualizer.plot_feature_activations(
                activations=feature_activations,
                feature_idx=feature_idx,
                title=f"Feature {feature_idx} Activations",
                save_path=os.path.join(output_dir, f"feature_{feature_idx}_activations.png"),
            )
    
    if visualize_all or visualize_heatmap:
        print("Visualizing feature heatmap...")
        visualizer.plot_feature_heatmap(
            activations=feature_activations,
            feature_indices=top_indices[:min(10, len(top_indices))],  # Use top 10 features for heatmap
            title=f"Top 10 Feature Activations Heatmap",
            save_path=os.path.join(output_dir, "feature_heatmap.png"),
        )
    
    print(f"Visualizations saved to {output_dir}")


def main():
    """Main function."""
    args = parse_args()
    
    # If no specific visualization is requested, visualize all
    if not any([
        args.visualize_importance,
        args.visualize_correlation,
        args.visualize_embedding,
        args.visualize_activations,
        args.visualize_heatmap,
    ]):
        args.visualize_all = True
    
    visualize_features(
        model_path=args.model_path,
        activations_path=args.activations_path,
        output_dir=args.output_dir,
        top_n=args.top_n,
        device=args.device,
        visualize_all=args.visualize_all,
        visualize_importance=args.visualize_importance,
        visualize_correlation=args.visualize_correlation,
        visualize_embedding=args.visualize_embedding,
        visualize_activations=args.visualize_activations,
        visualize_heatmap=args.visualize_heatmap,
        embedding_method=args.embedding_method,
        batch_size=args.batch_size,  # Pass batch size to function
    )


if __name__ == "__main__":
    main()

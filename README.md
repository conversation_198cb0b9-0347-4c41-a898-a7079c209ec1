# GenomeOcean Sparse Autoencoder (GOSAE)

A specialized framework for training Sparse Autoencoders (SAEs) on the GenomeOcean model (Mistral architecture) to discover, validate, and visualize biologically relevant features within the model's latent space.

## Overview

This project implements a sparse autoencoder framework specifically designed for extracting interpretable features from the GenomeOcean model's activations. The framework includes:

1. **Sparse Autoencoder Implementation**: A PyTorch implementation of sparse autoencoders with support for L1 regularization and BatchTopK sparsity mechanisms.
2. **DNA Tokenizer**: A specialized tokenizer for genomic sequences compatible with the GenomeOcean model.
3. **Activation Extraction**: Utilities for extracting activations from specific layers of the GenomeOcean model.
4. **Genomic Data Processing**: Tools for loading and processing genomic data from standard formats (FASTA, GenBank).
5. **Training Pipeline**: A comprehensive training pipeline with support for logging, checkpointing, and evaluation.
6. **Visualization Tools**: Utilities for visualizing and analyzing discovered features.

## Installation

### 1. Create a Conda Environment

First, create a conda environment with Python 3.11 and PyTorch:

```bash
# Create a new conda environment
conda create -n gosae python=3.11
conda activate gosae

```

### 2. Install GenomeOcean

Install GenomeOcean.

### 3. Install GOSAE

Finally, clone and install the GOSAE package:

```bash
# Clone the repository
git clone https://github.com/yourusername/gosae.git
cd gosae

# Install in development mode
pip install -e .
```

## Usage

### Train the model

Edit configs/default_config.json as neededed, or better, use a separate config file for each project.

```bash
python train_sae.py --config configs/default_config.json
```

Or specify parameters directly:

```bash
python train_sae.py \
    --data_dir data \
    --train_files human_genome.fa.gz \
    --val_files mouse_genome.fa.gz \
    --input_dim 4096 \
    --latent_dim 8192 \
    --hidden_dim 4096 \
    --sparsity_target 0.05 \
    --l1_coefficient 0.001 \
    --topk 32 \
    --model_name genomeocean \
    --target_layers model.layers.20.mlp.up_proj \
    --max_length 1000000 \
    --cache_dir cache \
    --use_cache \
    --output_dir outputs/sae_run1 \
    --batch_size 128 \
    --learning_rate 0.0003 \
    --num_epochs 50 \
    --save_interval 5 \
    --eval_interval 5 \
    --seed 42 \
    --device cuda
```

### Extracting Activations from GenomeOcean Model

python 
```

#### Visualizing Features

After training a sparse autoencoder and extracting activations, you can generate a bed file with the discovered features using the provided script:

```bash
python python annotate_sequences.py \
  --config configs/default_config.json \
  --output_dir outputs/sae_zymo \
  --fasta_file test_data/zymo/Enterococcus_faecalis_complete_genome.fasta.gz \
  --sae_model_path outputs/sae_zymo/best_model.pt \
  --num_top_features 10 
```


## Configuration

The framework uses a hierarchical configuration system with three main components:

1. **SparseAutoencoderConfig**: Configuration for the sparse autoencoder model.
2. **GenomeOceanConfig**: Configuration for GenomeOcean model integration.
3. **TrainingConfig**: Configuration for the training pipeline.

These are combined into a master `GOSAEConfig` that can be saved to and loaded from JSON files.



## GenomeOcean Models

This framework supports all three GenomeOcean models with their specific characteristics:

| Model | Token Limit | Embedding Size | Default for |
|-------|------------|---------------|-------------|
| `pGenomeOcean/GenomeOcean-100M` | 1024 | 768 | Development and testing |
| `pGenomeOcean/GenomeOcean-500M` | 1024 | 768 | Medium-scale experiments |
| `pGenomeOcean/GenomeOcean-4B` | 10240 | 3072 | Production use |

The framework automatically adjusts parameters based on the selected model, including:

- Maximum token length
- Embedding size
- Recommended latent dimension for the sparse autoencoder

## Requirements

- Python 3.11+
- PyTorch 2.4+
- genomeocean
- transformers
- numpy
- matplotlib
- pandas
- scikit-learn
- tqdm
- biopython
- wandb (optional)

## License

TBD

# Zymo Mock Community Genomes

This directory contains genome files from the Zymo mock community, which is a collection of bacterial and fungal genomes commonly used for benchmarking genomic tools.

## Files

- `Bacillus_subtilis_complete_genome.fasta`: Complete genome of Bacillus subtilis
- `Cryptococcus_neoformans_draft_genome.renamed.fasta`: Draft genome of Cryptococcus neoformans
- `Enterococcus_faecalis_complete_genome.fasta`: Complete genome of Enterococcus faecalis
- `Escherichia_coli_complete_genome.fasta`: Complete genome of Escherichia coli
- `Lactobacillus_fermentum_complete_genome.fasta`: Complete genome of Lactobacillus fermentum
- `Listeria_monocytogenes_complete_genome.fasta`: Complete genome of Listeria monocytogenes
- `Pseudomonas_aeruginosa_complete_genome.fasta`: Complete genome of Pseudomonas aeruginosa
- `Saccharomyces_cerevisiae_draft_genome.renamed.fasta`: Draft genome of Saccharomyces cerevisiae
- `Salmonella_enterica_complete_genome.fasta`: Complete genome of Salmonella enterica
- `Staphylococcus_aureus_complete_genome.fasta`: Complete genome of Staphylococcus aureus

## Usage

These genome files are used for testing the GOSAE framework. The default genome file used for testing is `Staphylococcus_aureus_complete_genome.fasta`, but you can specify a different file with the `--genome_file` argument when running tests:

```bash
python run_tests.py --genome_file Escherichia_coli_complete_genome.fasta
```

## Source

These genomes are derived from the Zymo mock community, which is a collection of microbial genomes used for benchmarking genomic tools. The original source of these genomes is the Zymo Research Corporation.

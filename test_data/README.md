# Test Data for GOSAE

This directory contains genomic data files used for testing the GOSAE framework.

## Directories

- `zymo/`: Contains genomic data files from the Zymo mock community, which is a collection of bacterial and fungal genomes commonly used for benchmarking genomic tools.

## Usage

These data files are used by the test scripts in the `tests/` directory. When running tests with:

```bash
python run_tests.py
```

The test scripts will automatically use the appropriate data files from this directory.

## File Formats

The test data includes:

- FASTA files (`.fasta`): Contains genomic sequences in FASTA format.
- GenBank files (`.gb`, `.gbk`): Contains genomic sequences with annotations in GenBank format.

## Adding New Test Data

When adding new test data, please:

1. Create a subdirectory with a descriptive name
2. Include a README.md file in the subdirectory explaining the source and purpose of the data
3. Use consistent file naming conventions
4. Keep file sizes reasonable for testing purposes

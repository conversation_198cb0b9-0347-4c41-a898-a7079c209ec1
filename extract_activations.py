#!/usr/bin/env python
"""
Script to extract activations from GenomeOcean model for a custom FASTA file.
"""

import os
import argparse
import torch
from pathlib import Path
import torch.distributed as dist

from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.utils.config import GenomeOceanConfig
from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_token_limit


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Extract activations from GenomeOcean model.")
    
    parser.add_argument("--fasta_file", type=str, required=True, help="Path to FASTA file.")
    parser.add_argument("--output_dir", type=str, default="activations_output", help="Output directory.")
    parser.add_argument("--model", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name or path.")
    parser.add_argument("--target_layers", type=str, nargs="+", 
                        default=["model.layers.8.mlp.up_proj"], 
                        help="Target layers for activation extraction.")
    parser.add_argument("--batch_size", type=int, default=2, help="Batch size for processing.")
    parser.add_argument("--max_sequences", type=int, default=0, help="Maximum number of sequences (0 for all).")
    parser.add_argument("--use_cache", action="store_true", help="Whether to use cached activations.")
    parser.add_argument("--local_rank", type=int, default=-1, help="Local rank for distributed training.")
    
    return parser.parse_args()


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Initialize distributed training if available
    if 'WORLD_SIZE' in os.environ:
        dist.init_process_group(backend='nccl')
        local_rank = dist.get_rank()
        torch.cuda.set_device(local_rank)
        is_distributed = True
        rank = dist.get_rank()
    else:
        is_distributed = False
        rank = 0

    # Create output directory on rank 0
    if rank == 0:
        os.makedirs(args.output_dir, exist_ok=True)
    
    # Initialize data processor
    if rank == 0:
        print(f"\n=== Initializing GenomicDataProcessor for model: {args.model} ===")
    data_processor = GenomicDataProcessor(
        tokenizer_model=args.model,
    )
    
    # Load and process sequences
    if rank == 0:
        print(f"\n=== Loading sequences from: {args.fasta_file} ===")
    seq_records = data_processor.load_fasta(args.fasta_file)
    processed_sequences = data_processor.process_sequences(seq_records)
    
    # Limit number of sequences if needed
    if args.max_sequences > 0 and len(processed_sequences) > args.max_sequences:
        processed_sequences = processed_sequences[:args.max_sequences]
        if rank == 0:
            print(f"Limited to {args.max_sequences} sequences")
    
    # Initialize activation extractor
    if rank == 0:
        print(f"\n=== Initializing GenomeOceanActivationExtractor for model: {args.model} ===")
    config = GenomeOceanConfig(
        model_name=args.model,
        target_layers=args.target_layers,
        cache_dir=os.path.join(args.output_dir, "cache"),
        use_cache=args.use_cache,
    )
    extractor = GenomeOceanActivationExtractor(config)
    
    # Extract activations for each target layer
    for target_layer in args.target_layers:
        if rank == 0:
            print(f"\n=== Extracting activations from layer: {target_layer} ===")
        
        # Extract activations (returns file paths)
        activation_files = extractor.extract_activations(
            sequences=processed_sequences,
            layer_name=target_layer,
            batch_size=args.batch_size,
        )
        
        if rank == 0:
            print(f"Activation batches cached in: {os.path.dirname(activation_files[0])}")
            # Optionally, load and print stats for the first batch as a sample
            if activation_files:
                first_batch = torch.load(activation_files[0])
                print(f"First batch shape: {first_batch.shape}")
                print(f"Activation statistics (first batch):")
                print(f"  Mean: {torch.mean(first_batch).item():.4f}")
                print(f"  Std: {torch.std(first_batch).item():.4f}")
                print(f"  Min: {torch.min(first_batch).item():.4f}")
                print(f"  Max: {torch.max(first_batch).item():.4f}")

    if is_distributed:
        dist.destroy_process_group()


if __name__ == "__main__":
    main()
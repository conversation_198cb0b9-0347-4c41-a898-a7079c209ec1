"""Visualization tools for genomic features discovered by SAEs."""

import os
import torch
import numpy as np
import matplotlib.pyplot as plt
from typing import List, Dict, Any, Optional, Union, Tuple
import seaborn as sns
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import pandas as pd


class FeatureVisualizer:
    """
    Visualizer for features discovered by Sparse Autoencoders.

    This class provides utilities for visualizing and analyzing features discovered
    by Sparse Autoencoders trained on GenomeOcean model activations.
    """

    def __init__(self, output_dir: str = "visualizations"):
        """
        Initialize the feature visualizer.

        Args:
            output_dir: Directory to save visualizations.
        """
        self.output_dir = output_dir
        os.makedirs(output_dir, exist_ok=True)

    def plot_feature_activations(
        self,
        activations: torch.Tensor,
        feature_idx: int,
        sequence_ids: Optional[List[str]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot activations of a specific feature across sequences.

        Args:
            activations: Tensor of feature activations with shape (num_sequences, num_features).
            feature_idx: Index of the feature to visualize.
            sequence_ids: List of sequence identifiers.
            title: Plot title.
            save_path: Path to save the plot.

        Returns:
            Matplotlib figure.
        """
        # Extract activations for the specified feature (detach first to avoid 'requires_grad' error)
        # Convert to float32 first to avoid issues with unsupported data types like BFloat16
        feature_activations = activations[:, feature_idx].detach().to(dtype=torch.float32).cpu().numpy()

        # Create sequence IDs if not provided
        if sequence_ids is None:
            sequence_ids = [f"Sequence {i+1}" for i in range(len(feature_activations))]

        # Create figure
        fig, ax = plt.subplots(figsize=(10, 6))

        # Plot activations
        ax.bar(sequence_ids, feature_activations)
        ax.set_xlabel("Sequence")
        ax.set_ylabel("Activation")
        ax.set_title(title or f"Feature {feature_idx} Activations")

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        return fig

    def plot_feature_heatmap(
        self,
        activations: torch.Tensor,
        feature_indices: Optional[List[int]] = None,
        sequence_ids: Optional[List[str]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot a heatmap of feature activations across sequences.

        Args:
            activations: Tensor of feature activations with shape (num_sequences, num_features).
            feature_indices: Indices of features to include in the heatmap.
            sequence_ids: List of sequence identifiers.
            title: Plot title.
            save_path: Path to save the plot.

        Returns:
            Matplotlib figure.
        """
        # Convert activations to numpy (detach first to avoid 'requires_grad' error)
        # Convert to float32 first to avoid issues with unsupported data types like BFloat16
        activations_np = activations.detach().to(dtype=torch.float32).cpu().numpy()

        # Select features if specified
        if feature_indices is not None:
            activations_np = activations_np[:, feature_indices]

        # Create sequence IDs if not provided
        if sequence_ids is None:
            sequence_ids = [f"Sequence {i+1}" for i in range(activations_np.shape[0])]

        # Create feature IDs
        if feature_indices is None:
            feature_ids = [f"Feature {i+1}" for i in range(activations_np.shape[1])]
        else:
            feature_ids = [f"Feature {i+1}" for i in feature_indices]

        # Create figure
        fig, ax = plt.subplots(figsize=(12, 8))

        # Plot heatmap
        sns.heatmap(
            activations_np,
            ax=ax,
            cmap="viridis",
            xticklabels=feature_ids,
            yticklabels=sequence_ids,
        )
        ax.set_xlabel("Feature")
        ax.set_ylabel("Sequence")
        ax.set_title(title or "Feature Activations Heatmap")

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        return fig

    def plot_feature_embedding(
        self,
        decoder_weights: torch.Tensor,
        method: str = "pca",
        feature_indices: Optional[List[int]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot an embedding of feature decoder weights.

        Args:
            decoder_weights: Tensor of decoder weights with shape (input_dim, latent_dim).
            method: Embedding method, either 'pca' or 'tsne'.
            feature_indices: Indices of features to include in the embedding.
            title: Plot title.
            save_path: Path to save the plot.

        Returns:
            Matplotlib figure.
        """
        # Convert weights to numpy (detach first to avoid 'requires_grad' error)
        # Convert to float32 first to avoid issues with unsupported data types like BFloat16
        weights_np = decoder_weights.detach().to(dtype=torch.float32).cpu().numpy()

        # Select features if specified
        if feature_indices is not None:
            weights_np = weights_np[:, feature_indices]

        # Transpose to get features as rows
        weights_np = weights_np.T

        # Apply dimensionality reduction
        if method.lower() == "pca":
            embedding = PCA(n_components=2).fit_transform(weights_np)
        elif method.lower() == "tsne":
            # Adjust perplexity based on number of samples
            # Perplexity must be less than the number of samples
            n_samples = weights_np.shape[0]
            perplexity = min(30, n_samples // 2)  # Default is 30, but ensure it's less than n_samples
            print(f"Using t-SNE with perplexity={perplexity} for {n_samples} samples")

            embedding = TSNE(n_components=2, perplexity=perplexity).fit_transform(weights_np)
        else:
            raise ValueError(f"Unsupported embedding method: {method}")

        # Create feature IDs
        if feature_indices is None:
            feature_ids = [f"Feature {i+1}" for i in range(weights_np.shape[0])]
        else:
            feature_ids = [f"Feature {i+1}" for i in feature_indices]

        # Create figure
        fig, ax = plt.subplots(figsize=(10, 8))

        # Plot embedding
        ax.scatter(embedding[:, 0], embedding[:, 1], s=50, alpha=0.8)

        # Add feature labels
        for i, feature_id in enumerate(feature_ids):
            ax.annotate(feature_id, (embedding[i, 0], embedding[i, 1]))

        ax.set_xlabel("Dimension 1")
        ax.set_ylabel("Dimension 2")
        ax.set_title(title or f"Feature Embedding ({method.upper()})")

        plt.tight_layout()

        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        return fig

    def plot_feature_importance(
        self,
        feature_importances: Dict[int, float],
        top_n: int = 20,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot feature importances.

        Args:
            feature_importances: Dictionary mapping feature indices to importance scores.
            top_n: Number of top features to include.
            title: Plot title.
            save_path: Path to save the plot.

        Returns:
            Matplotlib figure.
        """
        # Sort features by importance
        sorted_features = sorted(
            feature_importances.items(),
            key=lambda x: x[1],
            reverse=True,
        )

        # Select top N features
        top_features = sorted_features[:top_n]

        # Extract feature indices and importances
        feature_indices = [f"Feature {idx+1}" for idx, _ in top_features]
        importances = [imp for _, imp in top_features]

        # Create figure
        fig, ax = plt.subplots(figsize=(10, 6))

        # Plot importances
        ax.bar(feature_indices, importances)
        ax.set_xlabel("Feature")
        ax.set_ylabel("Importance")
        ax.set_title(title or f"Top {top_n} Feature Importances")

        # Rotate x-axis labels for better readability
        plt.xticks(rotation=45, ha="right")
        plt.tight_layout()

        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        return fig

    def plot_feature_correlation(
        self,
        activations: torch.Tensor,
        feature_indices: Optional[List[int]] = None,
        title: Optional[str] = None,
        save_path: Optional[str] = None,
    ) -> plt.Figure:
        """
        Plot correlation between features.

        Args:
            activations: Tensor of feature activations with shape (num_sequences, num_features).
            feature_indices: Indices of features to include in the correlation plot.
            title: Plot title.
            save_path: Path to save the plot.

        Returns:
            Matplotlib figure.
        """
        # Convert activations to numpy (convert to float32 first to avoid issues with BFloat16)
        activations_np = activations.detach().to(dtype=torch.float32).cpu().numpy()

        # Select features if specified
        if feature_indices is not None:
            activations_np = activations_np[:, feature_indices]

        # Create feature IDs
        if feature_indices is None:
            feature_ids = [f"F{i+1}" for i in range(activations_np.shape[1])]
        else:
            feature_ids = [f"F{i+1}" for i in feature_indices]

        # Compute correlation matrix
        corr_matrix = np.corrcoef(activations_np.T)

        # Create figure
        fig, ax = plt.subplots(figsize=(10, 8))

        # Plot correlation matrix
        sns.heatmap(
            corr_matrix,
            ax=ax,
            cmap="coolwarm",
            vmin=-1,
            vmax=1,
            xticklabels=feature_ids,
            yticklabels=feature_ids,
            annot=True if len(feature_ids) <= 20 else False,
            fmt=".2f" if len(feature_ids) <= 20 else None,
        )
        ax.set_title(title or "Feature Correlation Matrix")

        plt.tight_layout()

        # Save figure if requested
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches="tight")

        return fig

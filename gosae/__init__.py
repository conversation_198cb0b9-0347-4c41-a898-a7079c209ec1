"""
GenomeOcean Sparse Autoencoder (GOSAE)
======================================

A specialized framework for training Sparse Autoencoders (SAEs) on the GenomeOcean model
to discover, validate, and visualize biologically relevant features within the model's latent space.
"""

__version__ = "0.1.0"

# Import main components for easier access
from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.data.dna_tokenizer import DNATokenizer
from gosae.training.trainer import SparseAutoencoderTrainer
from gosae.visualization.feature_visualizer import FeatureVisualizer
from gosae.utils.config import SparseAutoencoderConfig, TrainingConfig

# Define public API
__all__ = [
    'SparseAutoencoder',
    'GenomeOceanActivationExtractor',
    'GenomicDataProcessor',
    'DNATokenizer',
    'SparseAutoencoderTrainer',
    'FeatureVisualizer',
    'SparseAutoencoderConfig',
    'TrainingConfig',
]

"""Sparse Autoencoder implementation for GenomeOcean."""

import torch
import torch.nn as nn
import torch.nn.functional as F
from typing import Tuple, Optional, Dict, Any

from gosae.utils.config import SparseAutoencoderConfig


class SparseAutoencoder(nn.Module):
    """
    Sparse Autoencoder for extracting interpretable features from GenomeOcean model activations.

    This implementation supports both L1 regularization and BatchTopK sparsity mechanisms.
    """

    def __init__(self, config: SparseAutoencoderConfig):
        """
        Initialize the Sparse Autoencoder.

        Args:
            config: Configuration for the Sparse Autoencoder.
        """
        super().__init__()

        self.config = config
        self.input_dim = config.input_dim
        self.latent_dim = config.latent_dim
        self.hidden_dim = config.hidden_dim or self.input_dim

        # Encoder
        self.encoder = nn.Sequential(
            nn.Linear(self.input_dim, self.hidden_dim),
            nn.ReLU(),
            nn.Linear(self.hidden_dim, self.latent_dim),
        )

        # Decoder
        #initialized with bias=False. This is a common practice in SAEs, but it assumes the input activations (x) are roughly centered around zero. 
        # If the activations have a significant mean, adding a bias to the decoder might improve reconstruction accuracy. 
        # It might be worth investigating the statistics of the GenomeOcean activations being fed into the SAE.
        self.decoder = nn.Linear(self.latent_dim, self.input_dim, bias=False)

        # Initialize weights
        self._init_weights()

        # Move model to the specified device
        self.to(self.config.device)
        print(f"Moved SparseAutoencoder to device: {self.config.device}")

    def _init_weights(self):
        """Initialize weights with specific distributions for better training."""
        # Initialize encoder weights
        for module in self.encoder.modules():
            if isinstance(module, nn.Linear):
                nn.init.kaiming_normal_(module.weight, nonlinearity='relu')
                if module.bias is not None:
                    nn.init.zeros_(module.bias)

        # Initialize decoder weights
        nn.init.kaiming_normal_(self.decoder.weight, nonlinearity='linear')

    def encode(self, x: torch.Tensor) -> torch.Tensor:
        """
        Encode input activations to sparse latent representations.

        Args:
            x: Input tensor of shape (batch_size, input_dim).

        Returns:
            Latent representation of shape (batch_size, latent_dim).
        """
        return self.encoder(x)

    def decode(self, z: torch.Tensor) -> torch.Tensor:
        """
        Decode latent representations back to input space.

        Args:
            z: Latent tensor of shape (batch_size, latent_dim).

        Returns:
            Reconstructed tensor of shape (batch_size, input_dim).
        """
        return self.decoder(z)

    def forward(self, x: torch.Tensor) -> Tuple[torch.Tensor, torch.Tensor]:
        """
        Forward pass through the autoencoder.

        Args:
            x: Input tensor of shape (batch_size, input_dim).

        Returns:
            Tuple of (reconstructed_input, latent_activations).
        """
        # The trainer should handle moving data to the correct device and dtype.
        # We can get the device and dtype from a parameter buffer.
        try:
            model_device = self.decoder.weight.device
            model_dtype = self.decoder.weight.dtype
        except AttributeError:
            raise ValueError("SparseAutoencoder has no decoder. Check model initialization.")

        if x.device != model_device or x.dtype != model_dtype:
             x = x.to(device=model_device, dtype=model_dtype)

        # Encode
        z = self.encode(x)

        # Apply BatchTopK if specified
        if self.config.topk > 0:
            z = self._batch_topk(z, k=self.config.topk)

        # Decode
        x_hat = self.decode(z)

        return x_hat, z

    def _batch_topk(self, z: torch.Tensor, k: int) -> torch.Tensor:
        """
        Apply BatchTopK sparsity mechanism.

        For each neuron, keep only the top-k activations across the batch and zero out the rest.

        Args:
            z: Latent activations of shape (batch_size, latent_dim).
            k: Number of activations to keep per neuron.

        Returns:
            Sparse latent activations.
        """
        batch_size = z.shape[0]
        if k >= batch_size:
            return z  # No sparsification needed

        # Find top-k values for each neuron across the batch
        topk_values, _ = torch.topk(z, k=k, dim=0)

        # Get the smallest value among the top-k for each neuron
        threshold = topk_values[-1, :]  # Shape: (latent_dim,)

        # Create a mask for values that are greater than or equal to the threshold
        mask = (z >= threshold).float()

        # Apply the mask to get sparse activations
        z_sparse = z * mask

        return z_sparse

    def compute_loss(
        self,
        x: torch.Tensor,
        x_hat: torch.Tensor,
        z: torch.Tensor
    ) -> Dict[str, torch.Tensor]:
        """
        Compute the loss for the Sparse Autoencoder.

        Args:
            x: Original input tensor.
            x_hat: Reconstructed input tensor.
            z: Latent activations.

        Returns:
            Dictionary containing the total loss and its components.
        """
        # Get model dtype from a parameter
        try:
            model_dtype = self.decoder.weight.dtype
        except AttributeError:
            raise ValueError("SparseAutoencoder has no decoder. Check model initialization.")

        # Convert tensors if needed
        if x.dtype != model_dtype:
            x = x.to(dtype=model_dtype)

        if x_hat.dtype != model_dtype:
            x_hat = x_hat.to(dtype=model_dtype)

        if z.dtype != model_dtype:
            z = z.to(dtype=model_dtype)

        # Reconstruction loss (MSE)
        recon_loss = F.mse_loss(x_hat, x)

        # L1 sparsity loss
        l1_loss = self.config.l1_coefficient * torch.mean(torch.abs(z))

        # Total loss
        total_loss = recon_loss + l1_loss

        # Compute sparsity (ensure it's float32 for backward compatibility)
        sparsity = torch.mean((z > 0).to(dtype=torch.float32))

        return {
            "total_loss": total_loss,
            "recon_loss": recon_loss,
            "l1_loss": l1_loss,
            "sparsity": sparsity,  # Average activation rate
        }

    def get_dead_neurons(self, threshold: float = 1e-5) -> torch.Tensor:
        """
        Identify dead neurons in the decoder.

        Args:
            threshold: Threshold for considering a neuron dead.

        Returns:
            Boolean tensor indicating dead neurons.
        """
        # A neuron is considered dead if all its decoder weights are close to zero
        decoder_norms = torch.norm(self.decoder.weight, dim=0)
        dead_neurons = decoder_norms < threshold
        return dead_neurons

    def save(self, path: str) -> None:
        """
        Save the model to a file.

        Args:
            path: Path to save the model.
        """
        torch.save({
            "state_dict": self.state_dict(),
            "config": self.config.to_dict(),
        }, path)

    @classmethod
    def load(cls, path: str, device: str = "cuda") -> "SparseAutoencoder":
        """
        Load the model from a file.

        Args:
            path: Path to load the model from.
            device: Device to load the model to.

        Returns:
            Loaded SparseAutoencoder model.
        """
        print(f"Loading SparseAutoencoder from {path} to device {device}")
        checkpoint = torch.load(path, map_location=device)

        # Handle different checkpoint formats
        if isinstance(checkpoint, dict):
            print(f"Checkpoint keys: {list(checkpoint.keys())}")

            # Case 1: Checkpoint has 'config' and 'state_dict' keys (our format)
            if 'config' in checkpoint and 'state_dict' in checkpoint:
                config_dict = checkpoint['config']
                state_dict = checkpoint['state_dict']
                print("Found standard checkpoint format with config and state_dict")

            # Case 2: Checkpoint has 'model_state_dict' and 'model_config' keys (trainer format)
            elif 'model_state_dict' in checkpoint and 'model_config' in checkpoint:
                state_dict = checkpoint['model_state_dict']
                config_dict = checkpoint['model_config']
                print("Found trainer checkpoint format with model_state_dict and model_config")

            # Case 3: Checkpoint is just the state_dict
            elif all(k.startswith('encoder.') or k.startswith('decoder.') for k in checkpoint.keys()):
                state_dict = checkpoint
                # Create a default config based on the state_dict
                encoder_weight = next((v for k, v in state_dict.items() if 'encoder.0.weight' in k), None)
                if encoder_weight is not None:
                    input_dim = encoder_weight.shape[1]
                    decoder_weight = next((v for k, v in state_dict.items() if 'decoder.weight' in k), None)
                    if decoder_weight is not None:
                        latent_dim = decoder_weight.shape[0]
                        config_dict = {
                            'input_dim': input_dim,
                            'latent_dim': latent_dim,
                            'hidden_dim': input_dim,  # Assume hidden_dim = input_dim
                            'device': device
                        }
                        print(f"Created config from state_dict: input_dim={input_dim}, latent_dim={latent_dim}")
                    else:
                        raise ValueError("Could not determine latent_dim from state_dict")
                else:
                    raise ValueError("Could not determine input_dim from state_dict")

            # Case 4: Unknown format
            else:
                raise ValueError(f"Unknown checkpoint format with keys: {list(checkpoint.keys())}")

        # Case 5: Checkpoint is the model itself
        elif isinstance(checkpoint, SparseAutoencoder):
            print("Checkpoint is a SparseAutoencoder instance")
            return checkpoint.to(device)

        # Case 6: Unknown type
        else:
            raise ValueError(f"Unknown checkpoint type: {type(checkpoint)}")

        # Create config and model
        config = SparseAutoencoderConfig.from_dict(config_dict, device=device)
        model = cls(config)
        model.load_state_dict(state_dict)

        # Ensure model is on the correct device
        model.to(device)
        print(f"Model loaded and moved to device: {next(model.parameters()).device}")

        return model

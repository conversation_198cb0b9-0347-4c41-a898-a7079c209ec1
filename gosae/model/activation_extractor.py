"""Activation extraction utilities for GenomeOcean model."""

import os
import torch
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple, Iterable
from tqdm import tqdm
import pickle
import hashlib
import torch.distributed
from torch.nn.parallel import DistributedDataParallel
from itertools import islice

from gosae.utils.config import GenomeOceanConfig
from gosae.data.dna_tokenizer import DNATokenizer
from gosae.utils.genomeocean_utils import get_model_token_limit, get_model_embedding_size, DEFAULT_MODEL


class GenomeOceanActivationExtractor:
    """
    Extracts activations from GenomeOcean model layers.

    This class provides utilities to extract and cache activations from specific layers
    of the GenomeOcean model (Mistral architecture) for use in training Sparse Autoencoders.
    """

    def __init__(self, config: GenomeOceanConfig):
        """
        Initialize the activation extractor.

        Args:
            config: Configuration for GenomeOcean model integration.
        """
        self.config = config
        self.model = None
        self.hooks = []
        self.activations = {}

        # Distributed training setup
        self.is_distributed = torch.distributed.is_available() and torch.distributed.is_initialized()
        self.use_data_parallel = False
        if self.is_distributed:
            self.rank = torch.distributed.get_rank()
            self.local_rank = int(os.environ['LOCAL_RANK'])
            self.world_size = torch.distributed.get_world_size()
            self.device = f'cuda:{self.local_rank}'
        else:
            self.rank = 0
            self.world_size = 1
            self.device = "cuda" if torch.cuda.is_available() else "cpu"
            if torch.cuda.is_available() and torch.cuda.device_count() > 1:
                self.use_data_parallel = True
                if self.rank == 0:
                    print(f"Using {torch.cuda.device_count()} GPUs with DataParallel for activation extraction.")

        # Create cache directory if needed (only on rank 0 to avoid race conditions)
        if self.rank == 0 and config.use_cache and config.cache_dir:
            os.makedirs(config.cache_dir, exist_ok=True)
        
        if self.is_distributed:
            torch.distributed.barrier() # Ensure cache dir is created before other ranks proceed

        # Load the model
        self._load_model()

    def _load_model(self):
        """Load the GenomeOcean model."""
        try:
            # This is a placeholder for loading the actual GenomeOcean model
            # In a real implementation, you would use the appropriate library
            # to load the Mistral-based GenomeOcean model
            from transformers import AutoModelForCausalLM

            model_path = self.config.model_path or self.config.model_name or DEFAULT_MODEL

            # Get model-specific settings
            token_limit = get_model_token_limit(model_path)
            embedding_size = get_model_embedding_size(model_path)

            if self.rank == 0:
                # Suppress verbose output
                # print(f"Loading GenomeOcean model: {model_path}")
                # print(f"  Token limit: {token_limit}")
                # print(f"  Embedding size: {embedding_size}")
                pass

            self.model = AutoModelForCausalLM.from_pretrained(
                model_path,
                trust_remote_code=True,
                torch_dtype=torch.bfloat16,
                attn_implementation="flash_attention_2",
            )

            # Initialize the DNA tokenizer
            self.tokenizer = DNATokenizer(
                model_name_or_path=model_path,
                max_length=self.config.max_length or token_limit,
                padding_side="left",
                trust_remote_code=True,
            )

            # Store model information
            self.model_path = model_path
            self.token_limit = token_limit
            self.embedding_size = embedding_size

            # Move model to device
            self.model.to(self.device)

            # Wrap model with DDP or DataParallel
            if self.is_distributed:
                self.model = DistributedDataParallel(
                    self.model,
                    device_ids=[self.rank % torch.cuda.device_count()] if 'cuda' in self.device else None,
                    find_unused_parameters=True # Important for models with conditional logic
                )
            elif self.use_data_parallel:
                self.model = torch.nn.DataParallel(self.model)

            self.model.eval()  # Set to evaluation mode

            # Register hooks for target layers
            self._register_hooks()

        except ImportError:
            if self.rank == 0:
                print("Warning: transformers library not found. Using dummy model for demonstration.")
            self.model = DummyGenomeOceanModel()
            self.tokenizer = DummyTokenizer()

    def list_available_layers(self):
        """List all available layers in the model."""
        layers = []
        model_to_inspect = self.model.module if self.is_distributed or self.use_data_parallel else self.model
        for name, _ in model_to_inspect.named_modules():
            layers.append(name)
        return layers

    def _register_hooks(self):
        """Register forward hooks to capture activations from target layers."""
        # Remove any existing hooks
        for hook in self.hooks:
            hook.remove()
        self.hooks = []

        # Helper function to capture activations
        def get_activation_hook(layer_name):
            def hook(module, input, output):
                # Handle different output types
                if isinstance(output, tuple):
                    self.activations[layer_name] = output[0].detach()
                elif isinstance(output, dict):
                    if 'hidden_states' in output:
                        self.activations[layer_name] = output['hidden_states'].detach()
                    else:
                        for key, value in output.items():
                            if isinstance(value, torch.Tensor):
                                self.activations[layer_name] = value.detach()
                                break
                elif isinstance(output, torch.Tensor):
                    self.activations[layer_name] = output.detach()
            return hook

        # Register hooks for each target layer
        for layer_name in self.config.target_layers:
            module = self._get_module_by_name(layer_name)
            if module is not None:
                hook = module.register_forward_hook(get_activation_hook(layer_name))
                self.hooks.append(hook)
            else:
                if self.rank == 0:
                    print(f"Warning: Could not find layer {layer_name} in the model")

    def _get_module_by_name(self, name: str) -> Optional[torch.nn.Module]:
        """
        Get a module by its name.
        """
        model_to_inspect = self.model.module if self.is_distributed or self.use_data_parallel else self.model
        try:
            if name.startswith('model.layers.'):
                parts = name.split('.')
                if len(parts) >= 3:
                    layer_num = int(parts[2].split('[')[0])
                    if hasattr(model_to_inspect, 'model') and hasattr(model_to_inspect.model, 'layers'):
                        layers = model_to_inspect.model.layers
                        if 0 <= layer_num < len(layers):
                            layer = layers[layer_num]
                            if len(parts) > 3:
                                for part in parts[3:]:
                                    if hasattr(layer, part):
                                        layer = getattr(layer, part)
                                    else:
                                        return None
                            return layer
            names = name.split('.')
            module = model_to_inspect
            if names[0] == 'model':
                names = names[1:]
            for n in names:
                if hasattr(module, n):
                    module = getattr(module, n)
                else:
                    return None
            return module
        except Exception:
            return None

    def extract_activations(
        self,
        sequences: Iterable[str],
        layer_name: str,
        batch_size: int = 4,
        use_cache: Optional[bool] = None,
        cache_prefix: str = "activations"
    ) -> List[str]:
        """
        Extract activations from a specific layer for a stream of genomic sequences
        and cache each batch to disk.
        """
        use_cache = use_cache if use_cache is not None else self.config.use_cache
        
        all_batch_files = []
        
        pbar = tqdm(desc=f"Extracting and caching {layer_name} activations", disable=(self.rank != 0))
        
        batch_num = 0
        while True:
            batch_sequences = list(islice(sequences, batch_size))
            if not batch_sequences:
                break

            if batch_num % self.world_size == self.rank:
                batch_cache_file = os.path.join(self.config.cache_dir, f"{cache_prefix}_{layer_name}_batch_{batch_num}.pt")
                all_batch_files.append(batch_cache_file)

                if not (use_cache and os.path.exists(batch_cache_file)):
                    processed_batch_activations = self._process_batch(batch_sequences, layer_name)
                    batch_activations_cpu = processed_batch_activations.cpu()
                    if use_cache and self.config.cache_dir:
                        os.makedirs(os.path.dirname(batch_cache_file), exist_ok=True)
                        torch.save(batch_activations_cpu, batch_cache_file)
            
            batch_num += 1
            pbar.update(1)

        pbar.close()

        if self.is_distributed:
            torch.distributed.barrier()

        # Gather all batch files from all ranks
        if self.is_distributed:
            gathered_files = [None] * self.world_size
            torch.distributed.all_gather_object(gathered_files, all_batch_files)
            all_batch_files = [item for sublist in gathered_files for item in sublist]

        # Clean up the model and clear CUDA memory
        self.close()

        return all_batch_files

    def _process_batch(self, sequences: List[str], layer_name: str) -> torch.Tensor:
        """
        Process a batch of sequences and extract activations.
        """
        if self.model is None:
            self._load_model()

        self.activations = {}
        inputs = self.tokenizer.encode(
            sequences,
            return_tensors="pt",
            padding=True,
            truncation=True,
        )
        device = self.model.module.device if self.is_distributed or self.use_data_parallel else self.model.device
        filtered_inputs = {k: v.to(device) for k, v in inputs.items() if k != 'token_type_ids'}

        with torch.no_grad():
            try:
                self.model(**filtered_inputs)
            except TypeError:
                essential_inputs = {k: filtered_inputs[k] for k in ['input_ids', 'attention_mask'] if k in filtered_inputs}
                self.model(**essential_inputs)

        if layer_name in self.activations:
            return self.activations[layer_name]
        else:
            raise ValueError(f"No activations found for layer {layer_name}. Available activations: {list(self.activations.keys())}")

    def extract_activations_with_offsets(
        self,
        sequences: List[str],
        layer_name: str,
    ) -> Tuple[torch.Tensor, Optional[List[List[Tuple[int, int]]]]]:
        """
        Process a batch of sequences and extract activations along with token offsets.
        """
        if self.model is None:
            self._load_model()

        self.activations = {}
        inputs = self.tokenizer.encode(
            sequences,
            return_tensors="pt",
            padding=True,
            truncation=True,
            return_offsets_mapping=True,
        )
        
        offset_mapping = inputs.pop("offset_mapping", None)

        device = self.model.module.device if self.is_distributed or self.use_data_parallel else self.model.device
        filtered_inputs = {k: v.to(device) for k, v in inputs.items() if k != 'token_type_ids'}

        with torch.no_grad():
            try:
                self.model(**filtered_inputs)
            except TypeError:
                essential_inputs = {k: filtered_inputs[k] for k in ['input_ids', 'attention_mask'] if k in filtered_inputs}
                self.model(**essential_inputs)

        if layer_name not in self.activations:
            raise ValueError(f"No activations found for layer {layer_name}. Available activations: {list(self.activations.keys())}")

        return self.activations[layer_name], offset_mapping.tolist() if offset_mapping is not None else None

    def get_activation_stats(
        self,
        activation_files: List[str],
    ) -> Dict[str, float]:
        """
        Compute statistics for activations from a list of cached files.
        """
        all_activations = []
        for file_path in activation_files:
            all_activations.append(torch.load(file_path))
        
        if not all_activations:
            return {"mean": 0, "std": 0, "min": 0, "max": 0, "sparsity": 0}

        activations = torch.cat(all_activations, dim=0)
        
        stats = {
            "mean": float(torch.mean(activations).item()),
            "std": float(torch.std(activations).item()),
            "min": float(torch.min(activations).item()),
            "max": float(torch.max(activations).item()),
            "sparsity": float(torch.mean((activations == 0).float()).item()),
        }
        return stats

    def close(self):
        """Clean up resources."""
        # Remove hooks
        for hook in self.hooks:
            hook.remove()
        self.hooks = []

        # Clear activations
        self.activations = {}

        # Clear model
        self.model = None

        # Clear CUDA cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()


class DummyGenomeOceanModel:
    """Dummy model for demonstration purposes."""

    def __init__(self):
        """Initialize the dummy model."""
        self.device = "cpu"

    def __call__(self, **kwargs):
        """Forward pass through the dummy model."""
        # Return dummy outputs
        return {"logits": torch.randn(1, 10, 100)}

    def eval(self):
        """Set the model to evaluation mode."""
        pass

    def to(self, device):
        """Move the model to a device."""
        self.device = device
        return self


class DummyTokenizer:
    """Dummy tokenizer for demonstration purposes."""

    def __call__(self, sequences, **kwargs):
        """Tokenize sequences."""
        # Return dummy inputs
        return {
            "input_ids": torch.randint(0, 100, (len(sequences), 10)),
            "attention_mask": torch.ones(len(sequences), 10),
        }

    def encode(self, sequences, **kwargs):
        """Encode sequences."""
        return self.__call__(sequences, **kwargs)

"""Utility functions for GenomeOcean models."""

from typing import Dict, Any, Optional

# GenomeOcean model token limits
MODEL_TOKEN_LIMITS = {
    'pGenomeOcean/GenomeOcean-100M': 1024,
    'pGenomeOcean/GenomeOcean-500M': 1024,
    'pGenomeOcean/GenomeOcean-4B': 10240
}

# GenomeOcean model embedding sizes
MODEL_EMBEDDING_SIZES = {
    'pGenomeOcean/GenomeOcean-100M': 768,
    'pGenomeOcean/GenomeOcean-500M': 768,
    'pGenomeOcean/GenomeOcean-4B': 3072
}

# Default model for development and testing
DEFAULT_MODEL = 'pGenomeOcean/GenomeOcean-100M'


def get_model_token_limit(model_name: str) -> int:
    """
    Get the maximum token limit for a GenomeOcean model.
    
    Args:
        model_name: Name of the GenomeOcean model.
        
    Returns:
        Maximum token limit for the model.
    """
    # Extract the base model name if it's a path
    base_model = model_name.split('/')[-1] if '/' in model_name else model_name
    
    # Check if the model name is in the dictionary
    for key in MODEL_TOKEN_LIMITS:
        if base_model in key or key in model_name:
            return MODEL_TOKEN_LIMITS[key]
    
    # Default to the smallest limit if model not found
    return MODEL_TOKEN_LIMITS[DEFAULT_MODEL]


def get_model_embedding_size(model_name: str) -> int:
    """
    Get the embedding size for a GenomeOcean model.
    
    Args:
        model_name: Name of the GenomeOcean model.
        
    Returns:
        Embedding size for the model.
    """
    # Extract the base model name if it's a path
    base_model = model_name.split('/')[-1] if '/' in model_name else model_name
    
    # Check if the model name is in the dictionary
    for key in MODEL_EMBEDDING_SIZES:
        if base_model in key or key in model_name:
            return MODEL_EMBEDDING_SIZES[key]
    
    # Default to the smallest embedding size if model not found
    return MODEL_EMBEDDING_SIZES[DEFAULT_MODEL]


def get_recommended_model_settings(model_name: str) -> Dict[str, Any]:
    """
    Get recommended settings for a GenomeOcean model.
    
    Args:
        model_name: Name of the GenomeOcean model.
        
    Returns:
        Dictionary of recommended settings.
    """
    token_limit = get_model_token_limit(model_name)
    embedding_size = get_model_embedding_size(model_name)
    
    # Approximate max sequence length in base pairs (about 5 bp per token)
    max_bp_length = token_limit * 5
    
    return {
        'token_limit': token_limit,
        'embedding_size': embedding_size,
        'max_bp_length': max_bp_length,
        'batch_size': 32 if '4B' in model_name else 64,  # Smaller batch size for larger model
    }

"""Configuration utilities for GenomeOcean Sparse Autoencoder."""

import os
import json
from dataclasses import dataclass, field, asdict
from typing import List, Optional, Dict, Any, Union

from gosae.utils.genomeocean_utils import (
    get_model_token_limit,
    get_model_embedding_size,
    DEFAULT_MODEL,
)


@dataclass
class SparseAutoencoderConfig:
    """Configuration for Sparse Autoencoder model."""

    # Model architecture
    latent_dim: int
    input_dim: Optional[int] = None
    hidden_dim: Optional[int] = None

    # Sparsity parameters
    sparsity_target: float = 0.05  # Target activation rate
    l1_coefficient: float = 1e-3   # L1 regularization strength

    # Training parameters
    learning_rate: float = 1e-3
    batch_size: int = 256
    num_epochs: int = 100

    # Optimizer parameters
    weight_decay: float = 0.0
    beta1: float = 0.9
    beta2: float = 0.999

    # BatchTopK parameters
    topk: int = 0  # If > 0, use BatchTopK sparsity

    # Misc
    device: str = "cuda"
    seed: int = 42

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return asdict(self)

    def save(self, path: str) -> None:
        """Save config to JSON file."""
        with open(path, "w") as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any], device: Optional[str] = None) -> "SparseAutoencoderConfig":
        """Create config from dictionary."""
        # Make a copy of the config dict to avoid modifying the original
        config_dict = config_dict.copy()

        # Update device if specified
        if device is not None:
            config_dict["device"] = device

        return cls(**config_dict)

    @classmethod
    def load(cls, path: str, device: Optional[str] = None) -> "SparseAutoencoderConfig":
        """Load config from JSON file."""
        with open(path, "r") as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict, device=device)


@dataclass
class GenomeOceanConfig:
    """Configuration for GenomeOcean model integration."""

    model_name: str = DEFAULT_MODEL
    model_path: Optional[str] = None

    # Layer configuration
    target_layers: List[str] = field(default_factory=lambda: [
        "model.layers.8",
        "model.layers.9",
        "model.layers.10",
        "model.layers.11"
    ])  # Default target layers for GenomeOcean model (layers 8-11 are recommended)

    # Sequence processing
    max_length: Optional[int] = None  # If None, uses the model's default token limit
    max_bp_length: Optional[int] = None  # If None, uses the model's recommended max bp length

    # Activation caching
    cache_dir: Optional[str] = None
    use_cache: bool = True

    def __post_init__(self):
        """Initialize derived attributes based on the model."""
        # Get the model name or path
        model = self.model_path or self.model_name

        # Set max_length if not provided
        if self.max_length is None:
            self.max_length = get_model_token_limit(model)

        # Set max_bp_length if not provided (approximate 5 bp per token)
        if self.max_bp_length is None:
            self.max_bp_length = self.max_length * 5

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return asdict(self)

    def save(self, path: str) -> None:
        """Save config to JSON file."""
        with open(path, "w") as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "GenomeOceanConfig":
        """Create config from dictionary."""
        return cls(**config_dict)

    @classmethod
    def load(cls, path: str) -> "GenomeOceanConfig":
        """Load config from JSON file."""
        with open(path, "r") as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


@dataclass
class TrainingConfig:
    """Configuration for training pipeline."""

    # Data configuration
    data_dir: str
    train_files: List[str]
    output_dir: str  # Moved up to be with other required parameters

    # Optional configuration
    val_files: Optional[List[str]] = None
    test_files: Optional[List[str]] = None
    save_interval: int = 10

    # Logging
    log_interval: int = 100
    use_wandb: bool = False
    wandb_project: Optional[str] = None

    # Evaluation
    eval_interval: int = 5
    gradient_accumulation_steps: int = 1

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        return asdict(self)

    def save(self, path: str) -> None:
        """Save config to JSON file."""
        with open(path, "w") as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "TrainingConfig":
        """Create config from dictionary."""
        return cls(**config_dict)

    @classmethod
    def load(cls, path: str) -> "TrainingConfig":
        """Load config from JSON file."""
        with open(path, "r") as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)


@dataclass
class GOSAEConfig:
    """Master configuration for GenomeOcean Sparse Autoencoder."""

    sae_config: SparseAutoencoderConfig
    genomeocean_config: GenomeOceanConfig
    training_config: Optional[TrainingConfig] = None

    def to_dict(self) -> Dict[str, Any]:
        """Convert config to dictionary."""
        d = {
            "sae_config": self.sae_config.to_dict(),
            "genomeocean_config": self.genomeocean_config.to_dict(),
        }
        if self.training_config:
            d["training_config"] = self.training_config.to_dict()
        return d

    def save(self, path: str) -> None:
        """Save config to JSON file."""
        with open(path, "w") as f:
            json.dump(self.to_dict(), f, indent=2)

    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> "GOSAEConfig":
        """Create config from dictionary."""
        return cls(
            sae_config=SparseAutoencoderConfig.from_dict(config_dict["sae_config"]),
            genomeocean_config=GenomeOceanConfig.from_dict(config_dict["genomeocean_config"]),
            training_config=TrainingConfig.from_dict(config_dict["training_config"]) if "training_config" in config_dict else None,
        )

    @classmethod
    def load(cls, path: str) -> "GOSAEConfig":
        """Load config from JSON file."""
        with open(path, "r") as f:
            config_dict = json.load(f)
        return cls.from_dict(config_dict)

"""
Utility functions for the GenomeOcean Sparse Autoencoder (GOSAE) framework.

This package contains configuration utilities and GenomeOcean-specific utilities.
"""

from gosae.utils.config import SparseAutoencoderConfig, TrainingConfig
from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_embedding_size

__all__ = [
    'SparseAutoencoderConfig',
    'TrainingConfig',
    'DEFAULT_MODEL',
    'get_model_embedding_size'
]


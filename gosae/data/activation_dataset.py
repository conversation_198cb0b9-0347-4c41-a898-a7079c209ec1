import torch
from torch.utils.data import Dataset
from typing import List
import numpy as np
from collections import OrderedDict

class ActivationDataset(Dataset):
    """
    A PyTorch Dataset for loading activations from a list of files lazily.
    This avoids loading all activations into memory at once.
    """
    def __init__(self, activation_files: List[str], cache_size: int = 10):
        """
        Args:
            activation_files: A list of paths to files containing activation tensors.
            cache_size: The number of files to keep in the cache.
        """
        self.activation_files = activation_files
        self.cache_size = cache_size
        self._cache = OrderedDict()
        self._file_lengths = {}
        self._cumulative_lengths = None
        self._total_length = None

    def _get_file_length(self, file_path):
        if file_path not in self._file_lengths:
            try:
                # This is still a bottleneck, but it's better than loading all files at once.
                self._file_lengths[file_path] = torch.load(file_path, map_location='cpu').shape[0]
            except Exception:
                self._file_lengths[file_path] = 0
        return self._file_lengths[file_path]

    def _calculate_lengths(self):
        """Calculate the lengths of all files and the total length."""
        if self._total_length is None:
            file_lengths = [self._get_file_length(f) for f in self.activation_files]
            self._cumulative_lengths = np.cumsum([0] + file_lengths)
            self._total_length = int(self._cumulative_lengths[-1])

    def __len__(self):
        if self._total_length is None:
            self._calculate_lengths()
        return self._total_length

    def __getitem__(self, idx):
        if self._total_length is None:
            self._calculate_lengths()

        if not (0 <= idx < self._total_length):
            raise IndexError("Index out of range")

        file_idx = np.searchsorted(self._cumulative_lengths, idx, side='right') - 1
        idx_in_file = idx - self._cumulative_lengths[file_idx]

        file_path = self.activation_files[file_idx]

        if file_path not in self._cache:
            if len(self._cache) >= self.cache_size:
                self._cache.popitem(last=False)  # Remove the least recently used item
            self._cache[file_path] = torch.load(file_path)
        
        self._cache.move_to_end(file_path) # Mark as recently used

        return self._cache[file_path][idx_in_file]
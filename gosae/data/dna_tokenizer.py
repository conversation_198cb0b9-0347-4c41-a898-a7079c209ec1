"""DNA tokenizer for GenomeOcean model."""

import os
import torch
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
from transformers import AutoTokenizer

from gosae.utils.genomeocean_utils import get_model_token_limit, DEFAULT_MODEL


class DNATokenizer:
    """
    Tokenizer for genomic sequences compatible with GenomeOcean model.

    This tokenizer is designed to work with the GenomeOcean model, which uses a BPE tokenizer
    with 4096 tokens. It supports tokenizing DNA sequences and handling long genomic sequences.
    """

    def __init__(
        self,
        model_name_or_path: str = DEFAULT_MODEL,
        max_length: Optional[int] = None,
        padding_side: str = "left",
        trust_remote_code: bool = True,
        use_fast: bool = True,
        cache_dir: Optional[str] = None,
    ):
        """
        Initialize the DNA tokenizer.

        Args:
            model_name_or_path: Path to the GenomeOcean model or tokenizer.
            max_length: Maximum sequence length. If None, uses the model's default token limit.
            padding_side: Side to add padding tokens ("left" or "right").
            trust_remote_code: Whether to trust remote code when loading the tokenizer.
            use_fast: Whether to use the fast tokenizer implementation.
            cache_dir: Directory to cache the tokenizer.
        """
        # Get the model's token limit if max_length is not specified
        if max_length is None:
            max_length = get_model_token_limit(model_name_or_path)

        try:
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name_or_path,
                model_max_length=max_length,
                padding_side=padding_side,
                use_fast=use_fast,
                trust_remote_code=trust_remote_code,
                cache_dir=cache_dir,
            )
            self.is_genomeocean = True
            print(f"Loaded GenomeOcean tokenizer for {model_name_or_path} with max_length={max_length}")
        except Exception as e:
            print(f"Warning: Could not load GenomeOcean tokenizer: {e}")
            print("Using fallback DNA tokenizer")
            self.tokenizer = FallbackDNATokenizer(max_length=max_length)
            self.is_genomeocean = False

        self.max_length = max_length
        self.model_name = model_name_or_path

    def encode(
        self,
        sequences: Union[str, List[str]],
        return_tensors: str = "pt",
        padding: Union[bool, str] = True,
        truncation: bool = True,
        add_special_tokens: bool = True,
        max_length: Optional[int] = None,
        return_offsets_mapping: bool = False,
    ) -> Dict[str, Any]:
        """
        Encode DNA sequences into token IDs.

        Args:
            sequences: DNA sequence or list of DNA sequences.
            return_tensors: Return type of tensors ("pt" for PyTorch, "np" for NumPy, "tf" for TensorFlow).
            padding: Whether to pad sequences to the same length.
            truncation: Whether to truncate sequences to max_length.
            add_special_tokens: Whether to add special tokens.
            max_length: Maximum sequence length. If None, uses self.max_length.

        Returns:
            Dictionary containing input_ids and attention_mask.
        """
        if isinstance(sequences, str):
            sequences = [sequences]

        # Remove spaces from sequences (GenomeOcean doesn't use spaces)
        sequences = [seq.replace(" ", "") for seq in sequences]

        # Use the model's max_length if not specified
        if max_length is None:
            max_length = self.max_length

        # Ensure padding is set correctly for consistent lengths
        if padding is True and truncation is True:
            # Use max_length padding to ensure all sequences have the same length
            padding = "max_length"

        if self.is_genomeocean:
            # Use the GenomeOcean tokenizer
            try:
                return self.tokenizer(
                    sequences,
                    return_tensors=return_tensors,
                    padding=padding,
                    truncation=truncation,
                    max_length=max_length,
                    add_special_tokens=add_special_tokens,
                    return_offsets_mapping=return_offsets_mapping,
                )
            except Exception as e:
                print(f"Warning: Error in GenomeOcean tokenizer: {e}")
                print(f"Sequences: {len(sequences)}, max_length: {max_length}")
                # Try with a more conservative approach
                return self.tokenizer(
                    sequences,
                    return_tensors=return_tensors,
                    padding=True,  # Just use regular padding
                    truncation=True,
                    max_length=max_length,
                    add_special_tokens=add_special_tokens,
                    return_offsets_mapping=return_offsets_mapping,
                )
        else:
            # Use the fallback tokenizer
            return self.tokenizer.encode(
                sequences,
                return_tensors=return_tensors,
                padding=padding,
                truncation=truncation,
                max_length=max_length,
            )

    def decode(
        self,
        token_ids: Union[List[int], torch.Tensor, np.ndarray],
        skip_special_tokens: bool = True,
    ) -> str:
        """
        Decode token IDs back to a DNA sequence.

        Args:
            token_ids: List of token IDs or tensor.
            skip_special_tokens: Whether to skip special tokens in the decoded string.

        Returns:
            Decoded DNA sequence.
        """
        if self.is_genomeocean:
            # Use the GenomeOcean tokenizer
            decoded = self.tokenizer.decode(token_ids, skip_special_tokens=skip_special_tokens)
            # Remove spaces (GenomeOcean doesn't use spaces in DNA sequences)
            return decoded.replace(" ", "").replace("\n", "")
        else:
            # Use the fallback tokenizer
            return self.tokenizer.decode(token_ids, skip_special_tokens=skip_special_tokens)

    def batch_encode(
        self,
        sequences: List[str],
        batch_size: int = 32,
        return_tensors: str = "pt",
        padding: Union[bool, str] = True,
        truncation: bool = True,
    ) -> Dict[str, torch.Tensor]:
        """
        Encode a large batch of sequences in smaller batches to avoid memory issues.

        Args:
            sequences: List of DNA sequences.
            batch_size: Batch size for processing.
            return_tensors: Return type of tensors.
            padding: Whether to pad sequences.
            truncation: Whether to truncate sequences.

        Returns:
            Dictionary containing input_ids and attention_mask for all sequences.
        """
        # Process all sequences at once to ensure consistent padding
        try:
            # Try to encode all sequences at once
            encoded = self.encode(
                sequences,
                return_tensors=return_tensors,
                padding="max_length" if padding else False,  # Use max_length padding to ensure consistent sizes
                truncation=truncation,
                max_length=self.max_length,  # Explicitly set max_length
            )
            return encoded
        except Exception as e:
            print(f"Warning: Failed to encode all sequences at once: {e}")
            print("Falling back to batch processing with additional padding...")

            # Process in batches with careful padding
            all_input_ids = []
            all_attention_masks = []
            max_length = 0

            # First pass: determine maximum length and encode in batches
            batch_encodings = []
            for i in range(0, len(sequences), batch_size):
                batch_sequences = sequences[i:i+batch_size]
                batch_encoded = self.encode(
                    batch_sequences,
                    return_tensors=return_tensors,
                    padding=True,  # Pad within batch
                    truncation=truncation,
                    max_length=self.max_length,  # Explicitly set max_length
                )
                batch_encodings.append(batch_encoded)

                # Update max length
                if return_tensors == "pt":
                    max_length = max(max_length, batch_encoded["input_ids"].shape[1])
                elif return_tensors == "np":
                    max_length = max(max_length, batch_encoded["input_ids"].shape[1])

            # Second pass: pad all batches to the same length
            for batch_encoded in batch_encodings:
                if return_tensors == "pt":
                    # Get current batch size and length
                    batch_size, current_length = batch_encoded["input_ids"].shape

                    # Pad if needed
                    if current_length < max_length:
                        # Pad input_ids
                        padding_size = max_length - current_length
                        pad_tensor = torch.full((batch_size, padding_size), self.tokenizer.pad_token_id if self.is_genomeocean else 0,
                                               dtype=batch_encoded["input_ids"].dtype,
                                               device=batch_encoded["input_ids"].device)
                        padded_input_ids = torch.cat([batch_encoded["input_ids"], pad_tensor], dim=1)

                        # Pad attention_mask
                        pad_mask = torch.zeros((batch_size, padding_size),
                                              dtype=batch_encoded["attention_mask"].dtype,
                                              device=batch_encoded["attention_mask"].device)
                        padded_attention_mask = torch.cat([batch_encoded["attention_mask"], pad_mask], dim=1)
                    else:
                        padded_input_ids = batch_encoded["input_ids"]
                        padded_attention_mask = batch_encoded["attention_mask"]

                    all_input_ids.append(padded_input_ids)
                    all_attention_masks.append(padded_attention_mask)
                elif return_tensors == "np":
                    # Similar padding for numpy arrays
                    batch_size, current_length = batch_encoded["input_ids"].shape

                    if current_length < max_length:
                        # Pad input_ids
                        padding_size = max_length - current_length
                        pad_array = np.full((batch_size, padding_size), self.tokenizer.pad_token_id if self.is_genomeocean else 0,
                                           dtype=batch_encoded["input_ids"].dtype)
                        padded_input_ids = np.concatenate([batch_encoded["input_ids"], pad_array], axis=1)

                        # Pad attention_mask
                        pad_mask = np.zeros((batch_size, padding_size), dtype=batch_encoded["attention_mask"].dtype)
                        padded_attention_mask = np.concatenate([batch_encoded["attention_mask"], pad_mask], axis=1)
                    else:
                        padded_input_ids = batch_encoded["input_ids"]
                        padded_attention_mask = batch_encoded["attention_mask"]

                    all_input_ids.append(padded_input_ids)
                    all_attention_masks.append(padded_attention_mask)

            # Concatenate all batches
            if return_tensors == "pt":
                return {
                    "input_ids": torch.cat(all_input_ids, dim=0),
                    "attention_mask": torch.cat(all_attention_masks, dim=0),
                }
            elif return_tensors == "np":
                return {
                    "input_ids": np.concatenate(all_input_ids, axis=0),
                    "attention_mask": np.concatenate(all_attention_masks, axis=0),
                }
            else:
                raise ValueError(f"Unsupported return_tensors: {return_tensors}")

    def get_vocab_size(self) -> int:
        """
        Get the vocabulary size of the tokenizer.

        Returns:
            Vocabulary size.
        """
        if self.is_genomeocean:
            return len(self.tokenizer)
        else:
            return self.tokenizer.vocab_size

    @property
    def vocab_size(self) -> int:
        """
        Get the vocabulary size of the tokenizer.

        Returns:
            Vocabulary size.
        """
        return self.get_vocab_size()

    def save_pretrained(self, save_directory: str) -> None:
        """
        Save the tokenizer to a directory.

        Args:
            save_directory: Directory to save the tokenizer.
        """
        if self.is_genomeocean:
            self.tokenizer.save_pretrained(save_directory)
        else:
            # For the fallback tokenizer, there's nothing to save
            os.makedirs(save_directory, exist_ok=True)
            with open(os.path.join(save_directory, "tokenizer_config.json"), "w") as f:
                f.write('{"type": "FallbackDNATokenizer"}')


class FallbackDNATokenizer:
    """
    Fallback tokenizer for DNA sequences when GenomeOcean tokenizer is not available.

    This tokenizer uses a simple character-level tokenization for DNA sequences.
    """

    def __init__(self, max_length: int = 10240):
        """
        Initialize the fallback DNA tokenizer.

        Args:
            max_length: Maximum sequence length.
        """
        self.max_length = max_length
        self.vocab = {
            "<pad>": 0,
            "<unk>": 1,
            "<cls>": 2,
            "<sep>": 3,
            "A": 4,
            "C": 5,
            "G": 6,
            "T": 7,
            "N": 8,
        }
        self.id_to_token = {v: k for k, v in self.vocab.items()}
        self.vocab_size = len(self.vocab)
        self.pad_token_id = self.vocab["<pad>"]

    def encode(
        self,
        sequences: List[str],
        return_tensors: str = "pt",
        padding: Union[bool, str] = True,
        truncation: bool = True,
        max_length: Optional[int] = None,
        return_offsets_mapping: bool = False,
    ) -> Dict[str, Any]:
        """
        Encode DNA sequences into token IDs.

        Args:
            sequences: List of DNA sequences.
            return_tensors: Return type of tensors.
            padding: Whether to pad sequences.
            truncation: Whether to truncate sequences.
            max_length: Maximum sequence length.

        Returns:
            Dictionary containing input_ids and attention_mask.
        """
        if max_length is None:
            max_length = self.max_length

        tokenized_sequences = []
        final_offset_mappings = [] if return_offsets_mapping else None
        if return_offsets_mapping: # Ensure it's an actual list if true
            final_offset_mappings = []

        for seq_str in sequences:
            tokens = []
            current_sequence_offsets = [] if return_offsets_mapping else None

            # Add CLS token
            tokens.append(self.vocab["<cls>"])
            if return_offsets_mapping:
                current_sequence_offsets.append((0, 0))

            # Tokenize the sequence characters
            char_pos_in_original_seq = 0
            for char_val in seq_str:
                # Check if we have space for this char AND the eventual SEP token
                if truncation and len(tokens) >= max_length - 1:
                    break
                
                if char_val in self.vocab:
                    tokens.append(self.vocab[char_val])
                else:
                    tokens.append(self.vocab["<unk>"]) # <unk> still represents one character
                
                if return_offsets_mapping:
                    current_sequence_offsets.append((char_pos_in_original_seq, char_pos_in_original_seq + 1))
                char_pos_in_original_seq += 1

            # Add SEP token
            # If already at max_length due to char tokens, replace the last token with SEP
            if truncation and len(tokens) >= max_length:
                tokens[-1] = self.vocab["<sep>"]
                if return_offsets_mapping:
                    current_sequence_offsets[-1] = (0,0) # Last token becomes SEP, losing its original offset
            # Else, if there's space or truncation is off
            elif len(tokens) < max_length or not truncation :
                tokens.append(self.vocab["<sep>"])
                if return_offsets_mapping:
                    current_sequence_offsets.append((0, 0))
            # If exactly at max_length-1 tokens and truncation is on, the next token (SEP) would be the max_length token.
            # This case is covered by len(tokens) < max_length allowing append.

            # Final truncation (defensive, ideally previous logic handles it)
            if truncation and len(tokens) > max_length:
                tokens = tokens[:max_length] # Ensure it's exactly max_length
                if tokens[-1] != self.vocab["<sep>"]: # If SEP was cut, force it
                    tokens[-1] = self.vocab["<sep>"]
                if return_offsets_mapping:
                    current_sequence_offsets = current_sequence_offsets[:max_length]
                    current_sequence_offsets[-1] = (0,0) # Ensure SEP offset is (0,0) if it was changed

            tokenized_sequences.append(tokens)
            if return_offsets_mapping:
                final_offset_mappings.append(current_sequence_offsets)

        # Determine the maximum length for padding
        if padding:
            if padding == "max_length":
                pad_length = max_length
            else:
                pad_length = min(max(len(seq) for seq in tokenized_sequences), max_length)

            # Pad sequences and offset mappings
            for i in range(len(tokenized_sequences)):
                current_len = len(tokenized_sequences[i])
                if current_len < pad_length:
                    padding_needed = pad_length - current_len
                    tokenized_sequences[i] = tokenized_sequences[i] + [self.vocab["<pad>"]] * padding_needed
                    if return_offsets_mapping and final_offset_mappings is not None: # Defensive check for None
                        final_offset_mappings[i] = final_offset_mappings[i] + [(0, 0)] * padding_needed
                # Ensure offset mappings are not longer than tokenized sequences if truncation happened differently
                if return_offsets_mapping and final_offset_mappings is not None and len(final_offset_mappings[i]) > len(tokenized_sequences[i]):
                    final_offset_mappings[i] = final_offset_mappings[i][:len(tokenized_sequences[i])]


        # Create attention masks
        attention_masks = []
        for seq in tokenized_sequences:
            mask = [1 if token != self.vocab["<pad>"] else 0 for token in seq]
            attention_masks.append(mask)

        # Convert to tensors
        if return_tensors == "pt":
            return {
                "input_ids": torch.tensor(tokenized_sequences),
                "attention_mask": torch.tensor(attention_masks),
            }
        elif return_tensors == "np":
            return {
                "input_ids": np.array(tokenized_sequences),
                "attention_mask": np.array(attention_masks),
            }
        else:
            return {
                "input_ids": tokenized_sequences,
                "attention_mask": attention_masks,
            }

    def decode(
        self,
        token_ids: Union[List[int], torch.Tensor, np.ndarray],
        skip_special_tokens: bool = True,
    ) -> str:
        """
        Decode token IDs back to a DNA sequence.

        Args:
            token_ids: List of token IDs or tensor.
            skip_special_tokens: Whether to skip special tokens in the decoded string.

        Returns:
            Decoded DNA sequence.
        """
        if isinstance(token_ids, torch.Tensor):
            token_ids = token_ids.tolist()
        elif isinstance(token_ids, np.ndarray):
            token_ids = token_ids.tolist()

        # If token_ids is a 2D list/array, take the first sequence
        if isinstance(token_ids[0], list):
            token_ids = token_ids[0]

        # Decode the tokens
        tokens = []
        for token_id in token_ids:
            if token_id in self.id_to_token:
                token = self.id_to_token[token_id]
                if skip_special_tokens and token in ["<pad>", "<unk>", "<cls>", "<sep>"]:
                    continue
                tokens.append(token)
            else:
                tokens.append("<unk>")

        # Join the tokens to form the sequence
        return "".join(tokens)

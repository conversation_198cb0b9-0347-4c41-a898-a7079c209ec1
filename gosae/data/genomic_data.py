"""Data processing utilities for genomic data."""

import os
import torch
import numpy as np
from typing import List, Dict, Any, Optional, Union, Tuple
from Bio import SeqIO
from Bio.Seq import Seq
from Bio.SeqRecord import SeqRecord
import random
import gzip

from gosae.data.dna_tokenizer import DNATokenizer
from gosae.utils.genomeocean_utils import get_recommended_model_settings, DEFAULT_MODEL


class GenomicDataProcessor:
    """
    Processor for genomic data.

    This class provides utilities for loading, processing, and batching genomic data
    for use with the GenomeOcean model and Sparse Autoencoders.
    """

    def __init__(
        self,
        max_length: Optional[int] = None,
        overlap: int = 0,
        random_crop: bool = False,
        tokenizer_model: str = DEFAULT_MODEL,
        tokenizer_max_length: Optional[int] = None,
    ):
        """
        Initialize the genomic data processor.

        Args:
            max_length: Maximum sequence length in base pairs. If None, uses the model's recommended settings.
            overlap: Overlap between consecutive sequences when splitting.
            random_crop: Whether to randomly crop sequences to max_length.
            tokenizer_model: Path to the GenomeOcean model or tokenizer.
            tokenizer_max_length: Maximum sequence length for the tokenizer (in tokens). If None, uses the model's default token limit.
        """
        # Get recommended settings for the model
        model_settings = get_recommended_model_settings(tokenizer_model)

        # Use recommended settings if not specified
        self.max_length = max_length if max_length is not None else model_settings['max_bp_length']
        self.overlap = overlap
        self.random_crop = random_crop
        self.model_name = tokenizer_model

        # Initialize the DNA tokenizer
        self.tokenizer = DNATokenizer(
            model_name_or_path=tokenizer_model,
            max_length=tokenizer_max_length,  # DNATokenizer will use the model's default if None
            padding_side="left",
            trust_remote_code=True,
        )

        # Suppress verbose output
        # print(f"Initialized GenomicDataProcessor for {tokenizer_model}")
        # print(f"  Max sequence length (bp): {self.max_length}")
        # print(f"  Max token length: {self.tokenizer.max_length}")
        pass

    def load_sequences(self, file_path: str):
        """Generator to parse sequences from a file."""
        file_format = None
        if file_path.endswith((".fa", ".fasta", ".fa.gz", ".fasta.gz")):
            file_format = "fasta"
        elif file_path.endswith((".gb", ".gbk", ".genbank", ".gb.gz", ".gbk.gz", ".genbank.gz")):
            file_format = "genbank"
        else:
            raise ValueError(f"Unsupported file format: {file_path}")

        open_fn = gzip.open if file_path.endswith(".gz") else open
        with open_fn(file_path, "rt") as handle:
            yield from SeqIO.parse(handle, file_format)

    def iter_process_file(self, file_path: str):
        """
        Generator to load and process sequences from a file.
        This avoids loading the entire file into memory.
        """
        for seq_record in self.load_sequences(file_path):
            seq_str = str(seq_record.seq).upper()

            if len(seq_str) > self.max_length:
                if self.random_crop:
                    start = random.randint(0, len(seq_str) - self.max_length)
                    yield seq_str[start:start+self.max_length]
                else:
                    for i in range(0, len(seq_str) - self.overlap, self.max_length - self.overlap):
                        end = min(i + self.max_length, len(seq_str))
                        yield seq_str[i:end]
            else:
                yield seq_str

    def load_and_process_directory(self, directory: str, file_pattern: str = "*"):
        """
        Generator to load and process all sequences from files in a directory.
        """
        import glob
        file_paths = glob.glob(os.path.join(directory, file_pattern))
        for file_path in file_paths:
            yield from self.iter_process_file(file_path)

    def tokenize_sequences(
        self,
        sequences: List[str],
        batch_size: int = 32,
        return_tensors: str = "pt",
        padding: Union[bool, str] = True,
        truncation: bool = True,
    ) -> Dict[str, torch.Tensor]:
        """
        Tokenize a list of DNA sequences using the DNA tokenizer.

        Args:
            sequences: List of DNA sequences.
            batch_size: Batch size for processing.
            return_tensors: Return type of tensors.
            padding: Whether to pad sequences.
            truncation: Whether to truncate sequences.

        Returns:
            Dictionary containing input_ids and attention_mask.
        """
        return self.tokenizer.batch_encode(
            sequences=sequences,
            batch_size=batch_size,
            return_tensors=return_tensors,
            padding=padding,
            truncation=truncation,
        )

    def prepare_for_model(
        self,
        sequences: List[str],
        batch_size: int = 32,
    ) -> torch.utils.data.DataLoader:
        """
        Prepare sequences for input to the model.

        Args:
            sequences: List of DNA sequences.
            batch_size: Batch size for the DataLoader.

        Returns:
            DataLoader containing tokenized sequences.
        """
        # Tokenize sequences
        tokenized = self.tokenize_sequences(
            sequences=sequences,
            batch_size=batch_size,
            return_tensors="pt",
            padding=True,
            truncation=True,
        )

        # Create dataset
        dataset = torch.utils.data.TensorDataset(
            tokenized["input_ids"],
            tokenized["attention_mask"],
        )

        # Create dataloader
        dataloader = torch.utils.data.DataLoader(
            dataset,
            batch_size=batch_size,
            shuffle=False,
        )

        return dataloader

    @staticmethod
    def get_sequence_stats(sequences: List[str]) -> Dict[str, Any]:
        """
        Compute statistics for a list of sequences.

        Args:
            sequences: List of sequence strings.

        Returns:
            Dictionary of statistics.
        """
        lengths = [len(seq) for seq in sequences]

        # Compute nucleotide frequencies
        nucleotides = "ACGTN"
        nucleotide_counts = {n: 0 for n in nucleotides}

        for seq in sequences:
            for n in nucleotides:
                nucleotide_counts[n] += seq.count(n)

        total_nucleotides = sum(nucleotide_counts.values())
        nucleotide_freqs = {n: count / total_nucleotides for n, count in nucleotide_counts.items()}

        # Compute statistics
        stats = {
            "num_sequences": len(sequences),
            "total_length": sum(lengths),
            "min_length": min(lengths),
            "max_length": max(lengths),
            "mean_length": np.mean(lengths),
            "median_length": np.median(lengths),
            "nucleotide_counts": nucleotide_counts,
            "nucleotide_frequencies": nucleotide_freqs,
        }

        return stats

"""Training utilities for Sparse Autoencoders."""

import os
import torch
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset, DistributedSampler
import numpy as np
from tqdm import tqdm
import time
from typing import Dict, Any, Optional, List, Tuple
import json
import torch.distributed as dist
from torch.nn.parallel import DistributedDataParallel

try:
    import wandb
    WANDB_AVAILABLE = True
except ImportError:
    WANDB_AVAILABLE = False

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.utils.config import SparseAutoencoderConfig, TrainingConfig


class SparseAutoencoderTrainer:
    """
    Trainer for Sparse Autoencoder models.

    This class provides utilities for training and evaluating Sparse Autoencoder models
    on activations extracted from the GenomeOcean model.
    """

    def __init__(
        self,
        model: SparseAutoencoder,
        training_config: TrainingConfig,
    ):
        """
        Initialize the trainer.

        Args:
            model: Sparse Autoencoder model to train.
            training_config: Configuration for training.
        """
        self.model = model
        self.config = training_config

        # Set up device and distributed training
        self.is_distributed = dist.is_available() and dist.is_initialized()
        self.use_data_parallel = False
        if self.is_distributed:
            self.rank = dist.get_rank()
            self.world_size = dist.get_world_size()
            self.device = f'cuda:{self.rank}'
            torch.cuda.set_device(self.rank)
            self.model.to(self.device)
            self.model = DistributedDataParallel(self.model, device_ids=[self.rank])
        else:
            self.rank = 0
            self.world_size = 1
            self.device = torch.device(model.config.device)
            self.model.to(self.device)
            if torch.cuda.is_available() and torch.cuda.device_count() > 1:
                self.use_data_parallel = True
                self.model = torch.nn.DataParallel(self.model)
                print(f"Using {torch.cuda.device_count()} GPUs with DataParallel.")

        # Print model information only on rank 0
        if self.rank == 0:
            model_dtype = next(self.model.parameters()).dtype
            print(f"Model is on device: {self.device} with dtype: {model_dtype}")

        # Set up optimizer
        self.optimizer = optim.Adam(
            self.model.parameters(),
            lr=model.config.learning_rate,
            weight_decay=model.config.weight_decay,
            betas=(model.config.beta1, model.config.beta2),
        )

        # Set up output directory (only on rank 0)
        if self.rank == 0:
            os.makedirs(training_config.output_dir, exist_ok=True)

        # Set up wandb (only on rank 0)
        self.use_wandb = training_config.use_wandb and WANDB_AVAILABLE and self.rank == 0
        if self.use_wandb:
            wandb.init(
                project=training_config.wandb_project or "gosae",
                config={
                    "sae_config": self.model.module.config.to_dict() if self.is_distributed or self.use_data_parallel else self.model.config.to_dict(),
                    "training_config": training_config.to_dict(),
                }
            )

    def train(
        self,
        train_dataset: torch.utils.data.Dataset,
        val_dataset: Optional[torch.utils.data.Dataset] = None,
    ) -> Dict[str, Any]:
        """
        Train the Sparse Autoencoder model.

        Args:
            train_dataset: A PyTorch Dataset for training.
            val_dataset: A PyTorch Dataset for validation.
                         If None, no validation is performed.

        Returns:
            Dictionary of training metrics.
        """
        # Create dataloaders with distributed samplers if needed
        train_sampler = DistributedSampler(train_dataset, num_replicas=self.world_size, rank=self.rank) if self.is_distributed else None
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.model.module.config.batch_size if self.is_distributed or self.use_data_parallel else self.model.config.batch_size,
            shuffle=(train_sampler is None),
            sampler=train_sampler
        )

        if val_dataset is not None:
            val_sampler = DistributedSampler(val_dataset, num_replicas=self.world_size, rank=self.rank, shuffle=False) if self.is_distributed else None
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.model.module.config.batch_size if self.is_distributed or self.use_data_parallel else self.model.config.batch_size,
                shuffle=False,
                sampler=val_sampler
            )
        else:
            val_loader = None

        # Training loop
        best_val_loss = float('inf')
        metrics = {
            "train_losses": [],
            "val_losses": [],
            "train_sparsities": [],
            "val_sparsities": [],
            "dead_neurons": [],
        }

        for epoch in range(self.model.module.config.num_epochs if self.is_distributed or self.use_data_parallel else self.model.config.num_epochs):
            if self.is_distributed:
                train_loader.sampler.set_epoch(epoch)

            # Train for one epoch
            train_metrics = self._train_epoch(train_loader, epoch)
            
            # Synchronize metrics across all processes
            if self.is_distributed:
                train_metrics = self._gather_metrics(train_metrics)

            if self.rank == 0:
                metrics["train_losses"].append(train_metrics["total_loss"])
                metrics["train_sparsities"].append(train_metrics["sparsity"])

                # Count dead neurons
                model_ref = self.model.module if self.is_distributed or self.use_data_parallel else self.model
                dead_neurons = model_ref.get_dead_neurons().sum().item()
                metrics["dead_neurons"].append(dead_neurons)

            # Evaluate on validation set
            if val_loader is not None and (epoch + 1) % self.config.eval_interval == 0:
                val_metrics = self._evaluate(val_loader)
                if self.is_distributed:
                    val_metrics = self._gather_metrics(val_metrics)

                if self.rank == 0:
                    metrics["val_losses"].append(val_metrics["total_loss"])
                    metrics["val_sparsities"].append(val_metrics["sparsity"])

                    # Save best model
                    if val_metrics["total_loss"] < best_val_loss:
                        best_val_loss = val_metrics["total_loss"]
                        self._save_checkpoint(os.path.join(self.config.output_dir, "best_model.pt"))

                    # Log validation metrics
                    print(f"Validation: {val_metrics}")
                    if self.use_wandb:
                        wandb.log({f"val/{k}": v for k, v in val_metrics.items()}, step=epoch)

            # Save checkpoint (only on rank 0)
            if self.rank == 0 and (epoch + 1) % self.config.save_interval == 0:
                self._save_checkpoint(os.path.join(self.config.output_dir, f"checkpoint_{epoch+1}.pt"))

        # Save final model and metrics (only on rank 0)
        if self.rank == 0:
            self._save_checkpoint(os.path.join(self.config.output_dir, "final_model.pt"))
            with open(os.path.join(self.config.output_dir, "metrics.json"), "w") as f:
                json.dump(metrics, f)

        return metrics

    def _train_epoch(self, dataloader: DataLoader, epoch: int) -> Dict[str, float]:
        """
        Train for one epoch.

        Args:
            dataloader: DataLoader for training data.
            epoch: Current epoch number.

        Returns:
            Dictionary of training metrics.
        """
        self.model.train()
        epoch_metrics = {
            "total_loss": 0.0,
            "recon_loss": 0.0,
            "l1_loss": 0.0,
            "sparsity": 0.0,
        }

        pbar = tqdm(dataloader, desc=f"Epoch {epoch+1}/{self.model.module.config.num_epochs if self.is_distributed or self.use_data_parallel else self.model.config.num_epochs}", disable=(self.rank != 0))
        for i, x in enumerate(pbar):
            x = x.to(self.device)
            
            # Reshape and move to device
            model_ref = self.model.module if self.is_distributed or self.use_data_parallel else self.model
            x = x.to(self.device).view(-1, model_ref.input_dim)

            # Forward pass
            x_hat, z = self.model(x)

            # Compute loss
            loss_dict = model_ref.compute_loss(x, x_hat, z)
            loss = loss_dict["total_loss"] / self.config.gradient_accumulation_steps

            # Backward pass
            loss.backward()

            # Optimizer step
            if (i + 1) % self.config.gradient_accumulation_steps == 0:
                self.optimizer.step()
                self.optimizer.zero_grad()

            # Update metrics
            for k, v in loss_dict.items():
                epoch_metrics[k] += v.item()

            # Update progress bar
            if self.rank == 0:
                pbar.set_postfix({k: f"{v:.4f}" for k, v in loss_dict.items()})

            # Log metrics (only on rank 0)
            if self.rank == 0 and (i + 1) % self.config.log_interval == 0:
                step = epoch * len(dataloader) + i
                if self.use_wandb:
                    wandb.log({f"train/{k}": v.item() for k, v in loss_dict.items()}, step=step)

        # Compute average metrics
        for k in epoch_metrics:
            epoch_metrics[k] /= len(dataloader)

        return epoch_metrics

    def _evaluate(self, dataloader: DataLoader) -> Dict[str, float]:
        """
        Evaluate the model on a dataset.

        Args:
            dataloader: DataLoader for evaluation data.

        Returns:
            Dictionary of evaluation metrics.
        """
        self.model.eval()
        eval_metrics = {
            "total_loss": 0.0,
            "recon_loss": 0.0,
            "l1_loss": 0.0,
            "sparsity": 0.0,
        }

        with torch.no_grad():
            for x in dataloader:
                x = x.to(self.device)

                # Reshape the input tensor
                model_ref = self.model.module if self.is_distributed or self.use_data_parallel else self.model
                x = x.view(-1, model_ref.input_dim)

                # Forward pass
                x_hat, z = self.model(x)

                # Compute loss
                loss_dict = model_ref.compute_loss(x, x_hat, z)

                # Update metrics
                for k, v in loss_dict.items():
                    eval_metrics[k] += v.item()

        # Compute average metrics
        for k in eval_metrics:
            eval_metrics[k] /= len(dataloader)

        return eval_metrics

    def _save_checkpoint(self, path: str) -> None:
        """
        Save a checkpoint.

        Args:
            path: Path to save the checkpoint.
        """
        model_state_dict = self.model.module.state_dict() if self.is_distributed or self.use_data_parallel else self.model.state_dict()
        model_config = self.model.module.config.to_dict() if self.is_distributed or self.use_data_parallel else self.model.config.to_dict()
        torch.save({
            "model_state_dict": model_state_dict,
            "optimizer_state_dict": self.optimizer.state_dict(),
            "model_config": model_config,
            "training_config": self.config.to_dict(),
        }, path)

    def load_checkpoint(self, path: str) -> None:
        """
        Load a checkpoint.

        Args:
            path: Path to load the checkpoint from.
        """
        checkpoint = torch.load(path, map_location=self.device)
        model_ref = self.model.module if self.is_distributed or self.use_data_parallel else self.model
        model_ref.load_state_dict(checkpoint["model_state_dict"])
        self.optimizer.load_state_dict(checkpoint["optimizer_state_dict"])

        # Move optimizer states to the correct device
        for state in self.optimizer.state.values():
            for k, v in state.items():
                if isinstance(v, torch.Tensor):
                    state[k] = v.to(self.device)

    def _gather_metrics(self, metrics: Dict[str, float]) -> Dict[str, float]:
        """Gather metrics from all processes in distributed training."""
        if not self.is_distributed:
            return metrics

        # Convert metrics to a tensor
        metric_tensor = torch.tensor([metrics[k] for k in sorted(metrics.keys())], device=self.device)
        
        # Gather tensors from all processes
        gathered_tensors = [torch.zeros_like(metric_tensor) for _ in range(self.world_size)]
        dist.all_gather(gathered_tensors, metric_tensor)

        # Average the metrics
        avg_metrics_tensor = torch.stack(gathered_tensors).mean(dim=0)
        
        # Convert back to dictionary
        avg_metrics = {k: avg_metrics_tensor[i].item() for i, k in enumerate(sorted(metrics.keys()))}
        
        return avg_metrics

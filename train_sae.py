#!/usr/bin/env python
"""
Train a Sparse Autoencoder on GenomeOcean model activations.

This script provides a command-line interface for training Sparse Autoencoders
on activations extracted from the GenomeOcean model.
"""

import os
import argparse
import torch
import json
from typing import Dict, Any, Optional, List, Tuple
import torch.distributed as dist

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.training.trainer import SparseAutoencoderTrainer
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.data.activation_dataset import ActivationDataset
from gosae.utils.config import (
    SparseAutoencoderConfig,
    GenomeOceanConfig,
    TrainingConfig,
    GOSAEConfig,
)
from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_embedding_size


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Train a Sparse Autoencoder on GenomeOcean model activations.")

    # Config file
    parser.add_argument("--config", type=str, help="Path to config file.")

    # Data arguments
    parser.add_argument("--data_dir", type=str, help="Directory containing genomic data.")
    parser.add_argument("--train_files", type=str, nargs="+", help="Training data files.")
    parser.add_argument("--val_files", type=str, nargs="+", help="Validation data files.")

    # Model arguments
    parser.add_argument("--input_dim", type=int, help="Input dimension.")
    parser.add_argument("--latent_dim", type=int, help="Latent dimension.")
    parser.add_argument("--hidden_dim", type=int, help="Hidden dimension.")
    parser.add_argument("--sparsity_target", type=float, help="Target sparsity.")
    parser.add_argument("--l1_coefficient", type=float, help="L1 regularization coefficient.")
    parser.add_argument("--topk", type=int, help="BatchTopK parameter.")

    # GenomeOcean arguments
    parser.add_argument("--model_name", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name.")
    parser.add_argument("--model_path", type=str, help="Path to GenomeOcean model.")
    parser.add_argument("--target_layers", type=str, nargs="+", help="Target layers for activation extraction.")
    parser.add_argument("--max_length", type=int, help="Maximum sequence length.")
    parser.add_argument("--cache_dir", type=str, help="Directory for caching activations.")
    parser.add_argument("--use_cache", action="store_true", help="Whether to use cached activations.")

    # Training arguments
    parser.add_argument("--output_dir", type=str, help="Output directory.")
    parser.add_argument("--batch_size", type=int, help="Batch size.")
    parser.add_argument("--learning_rate", type=float, help="Learning rate.")
    parser.add_argument("--num_epochs", type=int, help="Number of epochs.")
    parser.add_argument("--save_interval", type=int, help="Checkpoint save interval.")
    parser.add_argument("--eval_interval", type=int, help="Evaluation interval.")
    parser.add_argument("--use_wandb", action="store_true", help="Whether to use wandb.")
    parser.add_argument("--wandb_project", type=str, help="wandb project name.")
    parser.add_argument("--seed", type=int, help="Random seed.")
    parser.add_argument("--device", type=str, help="Device to use.")
    parser.add_argument("--local_rank", type=int, default=-1, help="Local rank for distributed training.")

    return parser.parse_args()


def create_config_from_args(args):
    """Create config from command-line arguments."""
    # Create SAE config
    sae_config = SparseAutoencoderConfig(
        input_dim=args.input_dim,
        latent_dim=args.latent_dim,
        hidden_dim=args.hidden_dim,
        sparsity_target=args.sparsity_target,
        l1_coefficient=args.l1_coefficient,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        num_epochs=args.num_epochs,
        topk=args.topk,
        device=args.device,
        seed=args.seed,
    )

    # Create GenomeOcean config
    genomeocean_config = GenomeOceanConfig(
        model_name=args.model_name,
        model_path=args.model_path,
        target_layers=args.target_layers,
        max_length=args.max_length,
        cache_dir=args.cache_dir,
        use_cache=args.use_cache,
    )

    # Create training config
    training_config = TrainingConfig(
        data_dir=args.data_dir,
        train_files=args.train_files,
        val_files=args.val_files,
        output_dir=args.output_dir,
        save_interval=args.save_interval,
        eval_interval=args.eval_interval,
        use_wandb=args.use_wandb,
        wandb_project=args.wandb_project,
    )

    # Create master config
    config = GOSAEConfig(
        sae_config=sae_config,
        genomeocean_config=genomeocean_config,
        training_config=training_config,
    )

    return config


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Initialize distributed training if available
    if 'WORLD_SIZE' in os.environ and int(os.environ['WORLD_SIZE']) > 1:
        dist.init_process_group(backend='nccl', init_method='env://')
        local_rank = int(os.environ['LOCAL_RANK'])
        torch.cuda.set_device(local_rank)
        is_distributed = True
        rank = dist.get_rank()
    else:
        is_distributed = False
        rank = 0

    # Load or create config
    if rank == 0:
        print("Loading or creating config...")
    if args.config:
        with open(args.config, "r") as f:
            config_dict = json.load(f)
        config = GOSAEConfig.from_dict(config_dict)
    else:
        config = create_config_from_args(args)

    # Set random seed
    torch.manual_seed(config.sae_config.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(config.sae_config.seed)

    # Create and save config only on rank 0
    if rank == 0:
        os.makedirs(config.training_config.output_dir, exist_ok=True)
        config.save(os.path.join(config.training_config.output_dir, "config.json"))

    # Determine input_dim if not provided
    if config.sae_config.input_dim is None:
        if rank == 0:
            print("Determining input_dim from GenomeOcean model...")
            config.sae_config.input_dim = get_model_embedding_size(
                model_name=config.genomeocean_config.model_name,
                layer_name=config.genomeocean_config.target_layers[0]
            )
            print(f"Determined input_dim: {config.sae_config.input_dim}")

    if is_distributed:
        # Broadcast the config from rank 0 to all other processes
        config_list = [config.to_dict()]
        dist.broadcast_object_list(config_list, src=0)
        if rank != 0:
            config = GOSAEConfig.from_dict(config_list[0])
        dist.barrier()

    # Load and process genomic data
    if rank == 0:
        print("Setting up data processing pipeline...")
    data_processor = GenomicDataProcessor(
        max_length=config.genomeocean_config.max_length,
    )

    def train_sequences_generator():
        for file in config.training_config.train_files:
            file_path = os.path.join(config.training_config.data_dir, file)
            yield from data_processor.iter_process_file(file_path)

    def val_sequences_generator():
        if config.training_config.val_files:
            for file in config.training_config.val_files:
                file_path = os.path.join(config.training_config.data_dir, file)
                yield from data_processor.iter_process_file(file_path)

    # Extract activations
    if rank == 0:
        print("Extracting activations...")
    activation_extractor = GenomeOceanActivationExtractor(config.genomeocean_config)
    target_layer = config.genomeocean_config.target_layers[0]

    train_activation_files = activation_extractor.extract_activations(
        sequences=train_sequences_generator(),
        layer_name=target_layer,
        batch_size=config.sae_config.batch_size,
        cache_prefix="train"
    )

    val_activation_files = None
    if config.training_config.val_files:
        val_activation_files = activation_extractor.extract_activations(
            sequences=val_sequences_generator(),
            layer_name=target_layer,
            batch_size=config.sae_config.batch_size,
            cache_prefix="val"
        )

    # Create datasets from cached files
    if rank == 0:
        print("Creating activation datasets...")
    train_dataset = ActivationDataset(train_activation_files, cache_size=10)
    if rank == 0:
        print(f"Training dataset size: {len(train_dataset)}")

    val_dataset = None
    if val_activation_files:
        val_dataset = ActivationDataset(val_activation_files, cache_size=5)
        if rank == 0:
            print(f"Validation dataset size: {len(val_dataset)}")

    # Create and train the Sparse Autoencoder
    if rank == 0:
        print("Creating and training the Sparse Autoencoder...")
    model = SparseAutoencoder(config.sae_config)
    trainer = SparseAutoencoderTrainer(model, config.training_config)

    metrics = trainer.train(
        train_dataset=train_dataset,
        val_dataset=val_dataset,
    )

    if rank == 0:
        print("Training complete!")
        print(f"Final metrics: {metrics}")

    if is_distributed:
        dist.destroy_process_group()


if __name__ == "__main__":
    main()

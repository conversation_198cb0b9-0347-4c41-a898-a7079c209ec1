#!/usr/bin/env python
"""
Wrapper script to run all GOSAE tests.

This script is a simple wrapper that calls the test_all.py script in the tests directory.
"""

import os
import sys
import subprocess


def main():
    """Main function."""
    # Get the directory of this script
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Build the path to test_all.py
    test_all_path = os.path.join(script_dir, "tests", "test_all.py")
    
    # Build the command
    cmd = [sys.executable, test_all_path] + sys.argv[1:]
    
    # Run the command
    try:
        process = subprocess.run(cmd)
        sys.exit(process.returncode)
    except Exception as e:
        print(f"Error running tests: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

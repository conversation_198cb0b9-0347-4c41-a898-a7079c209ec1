# Installation Instructions for GOSAE

This document provides detailed instructions for installing the GenomeOcean Sparse Autoencoder (GOSAE) framework.

## Prerequisites

- Python 3.11 or higher
- CUDA-compatible GPU (recommended for training)
- Git
- Biopython
- 
## Installation Methods

You can install GOSAE using one of the following methods:

### Method 1: Install from PyPI (Recommended for Users)

```bash
# Create and activate a conda environment
conda create -n gosae python=3.11
conda activate gosae

# Install PyTorch with CUDA support
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia

# Install GOSAE
pip install gosae

#install biopython
conda install -c conda-forge biopython
```

### Method 2: Install from Source (Recommended for Developers)

```bash
# Create and activate a conda environment
conda create -n gosae python=3.11
conda activate gosae

# Install PyTorch with CUDA support
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia

# Clone the repository
git clone https://github.com/jgi-genomeocean/gosae.git
cd gosae

# Install in development mode
pip install -e .
```

## Verifying Installation

To verify that the installation was successful, run the following command:

```bash
python -c "import gosae; print(f'GOSAE version: {gosae.__version__}')"
```

## Running Tests

To run the tests and verify that everything is working correctly:

```bash
# Run all tests
python run_tests.py --create_sample

# Run specific tests
python run_tests.py --tests genomic_data activation_extraction --create_sample
```

## Troubleshooting

If you encounter any issues during installation:

### Common Issues

1. **PyTorch CUDA Issues**:
   - Verify your CUDA installation: `nvidia-smi`
   - Check PyTorch CUDA availability: `python -c "import torch; print(torch.cuda.is_available())"`
   - If CUDA is not available, reinstall PyTorch with the correct CUDA version

2. **Missing Dependencies**:
   - If you encounter missing dependencies, install them manually:
     ```bash
     pip install -r requirements.txt
     ```

3. **GenomeOcean Issues**:
   - If you encounter issues with GenomeOcean, install it separately:
     ```bash
     pip install genomeocean
     ```
   - For more information, refer to the official documentation at https://github.com/jgi-genomeocean/genomeocean

4. **Visualization Issues**:
   - For visualization-related issues, make sure that matplotlib and seaborn are installed:
     ```bash
     pip install matplotlib seaborn
     ```

### Getting Help

If you continue to experience issues, please open an issue on the GitHub repository with the following information:

- Your operating system and version
- Python version (`python --version`)
- PyTorch version (`python -c "import torch; print(torch.__version__)"`)
- CUDA version (`nvidia-smi`)
- Full error message and traceback

## Advanced Installation

### Installing with Specific CUDA Version

If you need to use a specific CUDA version, you can install PyTorch accordingly:

```bash
# For CUDA 11.8
conda install pytorch torchvision torchaudio pytorch-cuda=11.8 -c pytorch -c nvidia

# For CUDA 12.1
conda install pytorch torchvision torchaudio pytorch-cuda=12.1 -c pytorch -c nvidia
```

### Installing without CUDA (CPU-only)

```bash
conda install pytorch torchvision torchaudio cpuonly -c pytorch
```

### Installing with Specific Dependencies

If you need specific versions of dependencies:

```bash
pip install -r requirements.txt
pip install genomeocean==0.1.0
pip install transformers==4.30.0
```

#!/usr/bin/env python
"""
Example script for training a sparse autoencoder on GenomeOcean model activations.
"""

import os
import torch
import argparse
from typing import List, Dict, Any

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.training.trainer import SparseAutoencoderTrainer
from gosae.utils.config import (
    SparseAutoencoderConfig,
    GenomeOceanConfig,
    TrainingConfig,
    GOSAEConfig,
)
from gosae.utils.genomeocean_utils import DEFAULT_MODEL, get_model_embedding_size


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Train a sparse autoencoder on GenomeOcean model activations.")

    # Data arguments
    parser.add_argument("--data_file", type=str, required=True, help="Path to genomic data file (FASTA or GenBank).")
    parser.add_argument("--output_dir", type=str, default="outputs", help="Output directory.")

    # Model arguments
    parser.add_argument("--genomeocean_model", type=str, default=DEFAULT_MODEL, help="GenomeOcean model name or path.")
    parser.add_argument("--target_layer", type=str, default="model.layers.8.mlp.up_proj", help="Target layer for activation extraction. Recommended layers for GenomeOcean models are 8, 9, 10, and 11.")
    parser.add_argument("--latent_dim", type=int, default=1536, help="Latent dimension for the sparse autoencoder (2x embedding size).")
    parser.add_argument("--l1_coefficient", type=float, default=1e-3, help="L1 regularization coefficient.")
    parser.add_argument("--topk", type=int, default=32, help="BatchTopK parameter (0 to disable).")

    # Training arguments
    parser.add_argument("--batch_size", type=int, default=32, help="Batch size.")
    parser.add_argument("--num_epochs", type=int, default=10, help="Number of epochs.")
    parser.add_argument("--learning_rate", type=float, default=3e-4, help="Learning rate.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use.")
    parser.add_argument("--seed", type=int, default=42, help="Random seed.")

    return parser.parse_args()


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()

    # Set random seed
    torch.manual_seed(args.seed)
    if torch.cuda.is_available():
        torch.cuda.manual_seed_all(args.seed)

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Create configurations
    genomeocean_config = GenomeOceanConfig(
        model_name=args.genomeocean_model,
        target_layers=[args.target_layer],
        max_length=10240,  # Maximum sequence length for GenomeOcean model
        cache_dir=os.path.join(args.output_dir, "cache"),
        use_cache=True,
    )

    # Load and process genomic data
    print("Loading and processing genomic data...")
    data_processor = GenomicDataProcessor(
        max_length=50000,  # Maximum sequence length in base pairs
        tokenizer_model=args.genomeocean_model,
        tokenizer_max_length=genomeocean_config.max_length,
    )

    sequences = data_processor.load_and_process_file(args.data_file)
    print(f"Loaded {len(sequences)} sequences")

    # Extract activations
    print("Extracting activations...")
    activation_extractor = GenomeOceanActivationExtractor(genomeocean_config)

    activations = activation_extractor.extract_activations(
        sequences=sequences,
        layer_name=args.target_layer,
        batch_size=args.batch_size,
    )

    # Get input dimension from activations
    input_dim = activations.shape[-1]
    print(f"Activation shape: {activations.shape}, input_dim: {input_dim}")

    # Get model embedding size
    embedding_size = get_model_embedding_size(args.genomeocean_model)
    print(f"Model embedding size: {embedding_size}")

    # Use 2x embedding size for latent dimension if not specified
    latent_dim = args.latent_dim or (embedding_size * 2)
    print(f"Using latent dimension: {latent_dim}")

    # Create sparse autoencoder configuration
    sae_config = SparseAutoencoderConfig(
        input_dim=input_dim,
        latent_dim=latent_dim,
        hidden_dim=input_dim,  # Use same dimension as input for hidden layer
        sparsity_target=0.05,
        l1_coefficient=args.l1_coefficient,
        learning_rate=args.learning_rate,
        batch_size=args.batch_size,
        num_epochs=args.num_epochs,
        topk=args.topk,
        device=args.device,
        seed=args.seed,
    )

    # Create training configuration
    training_config = TrainingConfig(
        data_dir="",  # Not used in this example
        train_files=[],  # Not used in this example
        output_dir=args.output_dir,
        save_interval=5,
        eval_interval=5,
        use_wandb=False,
    )

    # Create master configuration
    config = GOSAEConfig(
        sae_config=sae_config,
        genomeocean_config=genomeocean_config,
        training_config=training_config,
    )

    # Save configuration
    config.save(os.path.join(args.output_dir, "config.json"))

    # Create and train the sparse autoencoder
    print("Creating and training the sparse autoencoder...")
    model = SparseAutoencoder(sae_config)
    trainer = SparseAutoencoderTrainer(model, training_config)

    # Split activations into train and validation sets (90/10 split)
    split_idx = int(0.9 * len(activations))
    train_activations = activations[:split_idx]
    val_activations = activations[split_idx:]

    # Train the model
    metrics = trainer.train(
        train_activations=train_activations,
        val_activations=val_activations,
    )

    print("Training complete!")
    print(f"Final metrics: {metrics}")

    # Save the model
    model.save(os.path.join(args.output_dir, "final_model.pt"))

    # Save activations for later analysis
    torch.save(activations, os.path.join(args.output_dir, "activations.pt"))

    print(f"Model and activations saved to {args.output_dir}")


if __name__ == "__main__":
    main()

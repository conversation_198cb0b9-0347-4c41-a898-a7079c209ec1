#!/usr/bin/env python
"""
Example script for visualizing features discovered by a sparse autoencoder.
"""

import os
import torch
import argparse
import matplotlib.pyplot as plt
from typing import List, Dict, Any

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.visualization.feature_visualizer import FeatureVisualizer


def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(description="Visualize features discovered by a sparse autoencoder.")
    
    parser.add_argument("--model_path", type=str, required=True, help="Path to the trained sparse autoencoder model.")
    parser.add_argument("--activations_path", type=str, required=True, help="Path to the saved activations.")
    parser.add_argument("--output_dir", type=str, default="visualizations", help="Output directory for visualizations.")
    parser.add_argument("--top_n", type=int, default=20, help="Number of top features to visualize.")
    parser.add_argument("--device", type=str, default="cuda" if torch.cuda.is_available() else "cpu", help="Device to use.")
    
    return parser.parse_args()


def main():
    """Main function."""
    # Parse arguments
    args = parse_args()
    
    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)
    
    # Load the model
    print(f"Loading model from {args.model_path}...")
    model = SparseAutoencoder.load(args.model_path, device=args.device)
    
    # Load activations
    print(f"Loading activations from {args.activations_path}...")
    activations = torch.load(args.activations_path, map_location=args.device)
    
    # Create visualizer
    visualizer = FeatureVisualizer(output_dir=args.output_dir)
    
    # Get feature activations
    print("Computing feature activations...")
    with torch.no_grad():
        _, feature_activations = model(activations)
    
    # Compute feature importance (L1 norm of decoder weights)
    feature_importance = torch.norm(model.decoder.weight, dim=0).cpu().numpy()
    feature_importance_dict = {i: float(importance) for i, importance in enumerate(feature_importance)}
    
    # Visualize feature importance
    print("Visualizing feature importance...")
    visualizer.plot_feature_importance(
        feature_importances=feature_importance_dict,
        top_n=args.top_n,
        title=f"Top {args.top_n} Feature Importances",
        save_path=os.path.join(args.output_dir, "feature_importance.png"),
    )
    
    # Visualize feature correlations
    print("Visualizing feature correlations...")
    visualizer.plot_feature_correlation(
        activations=feature_activations,
        feature_indices=list(range(args.top_n)),
        title=f"Top {args.top_n} Feature Correlations",
        save_path=os.path.join(args.output_dir, "feature_correlation.png"),
    )
    
    # Visualize feature embedding
    print("Visualizing feature embedding...")
    visualizer.plot_feature_embedding(
        decoder_weights=model.decoder.weight,
        method="tsne",
        feature_indices=list(range(args.top_n)),
        title=f"Top {args.top_n} Feature Embedding (t-SNE)",
        save_path=os.path.join(args.output_dir, "feature_embedding.png"),
    )
    
    # Visualize individual feature activations
    print("Visualizing individual feature activations...")
    for i in range(min(5, args.top_n)):  # Visualize top 5 features
        visualizer.plot_feature_activations(
            activations=feature_activations,
            feature_idx=i,
            title=f"Feature {i+1} Activations",
            save_path=os.path.join(args.output_dir, f"feature_{i+1}_activations.png"),
        )
    
    # Visualize feature heatmap
    print("Visualizing feature heatmap...")
    visualizer.plot_feature_heatmap(
        activations=feature_activations,
        feature_indices=list(range(args.top_n)),
        title=f"Top {args.top_n} Feature Activations Heatmap",
        save_path=os.path.join(args.output_dir, "feature_heatmap.png"),
    )
    
    print(f"Visualizations saved to {args.output_dir}")


if __name__ == "__main__":
    main()

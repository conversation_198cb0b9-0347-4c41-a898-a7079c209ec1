
#!/usr/bin/env python
"""
Annotate input sequences using a trained Sparse Autoencoder (SAE)
and output the top activating features in BED format.
"""

import argparse
import os
import torch
import pandas as pd
from tqdm import tqdm
import json

from gosae.model.sparse_autoencoder import SparseAutoencoder
from gosae.model.activation_extractor import GenomeOceanActivationExtractor
from gosae.data.genomic_data import GenomicDataProcessor
from gosae.utils.config import GOSAEConfig, GenomeOceanConfig

def parse_args():
    """Parse command-line arguments."""
    parser = argparse.ArgumentParser(
        description="Annotate sequences with an SAE and output top features in BED format."
    )
    parser.add_argument("--config", type=str, required=True, help="Path to the configuration file.")
    parser.add_argument("--output_dir", type=str, required=True, help="Output directory.")
    parser.add_argument("--num_top_features", type=int, default=10, help="Number of top features to annotate")
    parser.add_argument("--fasta_file", type=str, required=True, help="Genome to annotate in fasta format")
    parser.add_argument("--sae_model_path", type=str, required=True, help="SAE model path")
    parser.add_argument("--feature_strength", type=float, default=0.98, help="Percentile of feature strength to keep (0.0 to 1.0)")
    parser.add_argument("--min_feature_size", type=int, default=10, help="Minimum size of a feature region in base pairs")
    return parser.parse_args()

def merge_and_filter_features(feature_df, strength_percentile, min_size):
    """Merge, filter, and process feature activations."""
    if feature_df.empty:
        return []

    # Filter by feature strength
    strength_threshold = feature_df['activation'].quantile(strength_percentile)
    strong_features = feature_df[feature_df['activation'] >= strength_threshold]

    if strong_features.empty:
        return []

    # Sort by sequence, then by start position
    strong_features = strong_features.sort_values(by=['seq_id', 'start'])

    merged_records = []
    
    for seq_id, group in strong_features.groupby('seq_id'):
        if group.empty:
            continue

        group_iter = group.iterrows()
        _, first_row = next(group_iter)
        
        current_start = first_row['start']
        current_end = first_row['end']
        current_activation_sum = first_row['activation']
        
        for _, row in group_iter:
            if row['start'] <= current_end:  # Overlapping or adjacent
                current_end = max(current_end, row['end'])
                current_activation_sum += row['activation']
            else:
                # Save the previous merged region if it meets the size criteria
                if current_end - current_start >= min_size:
                    merged_records.append({
                        "seq_id": seq_id,
                        "start": current_start,
                        "end": current_end,
                        "feature_id": first_row['feature_id'],
                        "activation": current_activation_sum
                    })
                # Start a new region
                current_start = row['start']
                current_end = row['end']
                current_activation_sum = row['activation']
        
        # Add the last processed region
        if current_end - current_start >= min_size:
            merged_records.append({
                "seq_id": seq_id,
                "start": current_start,
                "end": current_end,
                "feature_id": first_row['feature_id'],
                "activation": current_activation_sum
            })
            
    return merged_records

def main():
    args = parse_args()

    # Load configuration from the file
    with open(args.config, 'r') as f:
        config_dict = json.load(f)
    config = GOSAEConfig.from_dict(config_dict)

    # Extract specific configuration objects
    sae_config = config.sae_config
    genomeocean_config = config.genomeocean_config

    # Setup device
    device = torch.device(sae_config.device if torch.cuda.is_available() else "cpu")
    print(f"Using device: {device}")

    # Create output directory
    os.makedirs(args.output_dir, exist_ok=True)

    # Load the trained Sparse Autoencoder model
    print(f"Loading SAE model from {args.sae_model_path}...")
    sae_model = SparseAutoencoder.load(args.sae_model_path, device=device)
    sae_model.eval()
    print(f"SAE model loaded. Input dimension: {sae_model.config.input_dim}, Latent dimension: {sae_model.config.latent_dim}")

    # Initialize the GenomicDataProcessor
    print("Initializing GenomicDataProcessor...")
    data_processor = GenomicDataProcessor(
        max_length=genomeocean_config.max_length,
        tokenizer_model=genomeocean_config.model_name
    )

    # Initialize the GenomeOceanActivationExtractor
    print("Initializing GenomeOceanActivationExtractor...")
    activation_extractor = GenomeOceanActivationExtractor(genomeocean_config)
    target_layer = genomeocean_config.target_layers[0]
    print(f"Activation extractor initialized for layer: {target_layer}")

    # Load sequences from the FASTA file
    print(f"Loading sequences from {args.fasta_file}...")
    raw_sequences = data_processor.load_sequences(args.fasta_file)

    # Process each sequence and collect all activations
    print("Processing sequences and extracting SAE features...")
    all_activations = []
    for seq_record in tqdm(raw_sequences, desc="Processing sequences"):
        seq_id = seq_record.id
        original_seq_str = str(seq_record.seq)

        # Extract activations for the current sequence
        activations_tensor, offset_mapping = activation_extractor.extract_activations_with_offsets(
            sequences=[original_seq_str],
            layer_name=target_layer
        )

        # Validate activation dimension
        if activations_tensor.shape[2] != sae_model.config.input_dim:
            raise ValueError(
                f"Activation dimension ({activations_tensor.shape[2]}) from layer '{target_layer}' "
                f"does not match SAE input dimension ({sae_model.config.input_dim})."
            )

        # Get latent features from the SAE model
        with torch.no_grad():
            _, latent_features = sae_model(activations_tensor.to(device))

        token_offsets = offset_mapping[0] if offset_mapping else []
        
        # Collect all feature activations for the current sequence
        for token_idx, (start, end) in enumerate(token_offsets):
            if start == end:
                continue

            token_features = latent_features[0, token_idx, :]
            active_indices = torch.nonzero(token_features).squeeze()

            if active_indices.numel() == 0:
                continue
            if active_indices.numel() == 1:
                active_indices = [active_indices]

            for feature_idx in active_indices:
                feature_idx_item = feature_idx.item()
                activation_value = token_features[feature_idx_item].item()
                all_activations.append({
                    "seq_id": seq_id,
                    "start": start,
                    "end": end,
                    "feature_id": feature_idx_item,
                    "activation": activation_value
                })

    if not all_activations:
        print("No feature activations found for any sequence.")
        return

    # Create a DataFrame from all activations
    activations_df = pd.DataFrame(all_activations)

    # Find the top N most frequent features across all sequences
    feature_counts = activations_df['feature_id'].value_counts()
    top_feature_ids = feature_counts.nlargest(args.num_top_features).index.tolist()

    print(f"Top {len(top_feature_ids)} most frequent features: {top_feature_ids}")

    # Process each top feature
    all_processed_records = []
    for feature_id in tqdm(top_feature_ids, desc="Processing top features"):
        feature_df = activations_df[activations_df['feature_id'] == feature_id]
        processed_records = merge_and_filter_features(
            feature_df, args.feature_strength, args.min_feature_size
        )
        all_processed_records.extend(processed_records)

    if not all_processed_records:
        print("No features met the specified strength and size criteria.")
        return

    # Create a final DataFrame and sort it
    final_df = pd.DataFrame(all_processed_records)
    final_df = final_df.sort_values(by=['seq_id', 'start'])

    # Generate BED file records
    bed_records = []
    for _, row in final_df.iterrows():
        bed_records.append(
            f"{row['seq_id']}\t{row['start']}\t{row['end']}\t"
            f"feature_{int(row['feature_id'])}\t{row['activation']:.4f}\t."
        )

    # Save the BED file
    output_bed_path = os.path.join(args.output_dir, "top_frequent_features.bed")
    
    with open(output_bed_path, "w") as f:
        f.write("\n".join(bed_records))

    print(f"Activations for top {args.num_top_features} most frequent features saved to {output_bed_path}")

    activation_extractor.close()
    print("Script finished.")

if __name__ == "__main__":
    main()
